# Análisis Arquitectónico - Partner Payments Library

## Estructura General del Proyecto

### Organización de Directorios
```
projects/partner-payments/src/lib/
├── checkout/                    # Módulo principal de checkout
│   ├── components/             # Componentes reutilizables
│   ├── pages/                  # Páginas principales del flujo
│   │   ├── payment-orchestrator/  # ⭐ COMPONENTE PRINCIPAL
│   │   ├── select-installment/    # Selección de cuotas
│   │   ├── plan-confirmation/     # Confirmación del plan
│   │   └── errors/               # Manejo de errores
│   ├── services/              # Servicios de negocio
│   ├── store/                 # Estado global (NgRx)
│   ├── interfaces/            # Definiciones de tipos
│   └── enums/                # Enumeraciones
└── payment-methods/           # Módulo de métodos de pago
```

## Análisis del Payment Orchestrator

### Componente Principal: `PaymentOrchestratorComponent`

**Ubicación**: `projects/partner-payments/src/lib/checkout/pages/payment-orchestrator/payment-orchestrator.component.ts`

**Responsabilidades Actuales**:
1. **Validación de Token**: Verifica `TOKEN_LOAN` en localStorage
2. **Carga de Datos**: Obtiene información de checkout y datos del cliente
3. **Decisión de Flujo**: Determina si mostrar selección de cuotas o ir directo a confirmación
4. **Manejo de Errores**: Procesa errores del API

### Lógica de Decisión Actual

```typescript
// Línea 67-72: Lógica actual de decisión de flujo
if (this.hasDynamicInstallments(data.schemes)) {
  this._pagesFacade.setCurrentPage(Pages.selectPlan);
} else {
  this._pagesFacade.setCurrentPage(Pages.planConfirmation);
  this._schemeFacade.setInfoCheckout(data.schemes[0]);
}
```

**Método `hasDynamicInstallments`**:
```typescript
// Línea 82-84: Validación de cuotas dinámicas
private hasDynamicInstallments(schemes: IScheme[]): boolean {
  return !schemes.some(scheme => scheme.id === -1) && schemes.length > 1;
}
```

### Estructura de Datos

#### Interface `IScheme`
```typescript
interface IScheme {
  id: number;                    // ⭐ Campo clave para matching
  installments: number;          // ⭐ Campo clave para query params
  // ... otros campos financieros
}
```

#### Interface `QueryParams` (Ya Definida)
```typescript
interface QueryParams {
  installments?: string;        // ⭐ Parámetro objetivo
  offerId?: string;            // ⭐ Parámetro objetivo
}
```

## Flujo de Navegación

### Enums de Páginas
```typescript
enum Pages {
  default = '',
  selectPlan = 'select-plan',           // Selección de cuotas
  planConfirmation = 'checkout',        // Confirmación del plan
  // ... otros
}
```

### Facades Utilizados

1. **PagesFacade**: Control de navegación entre páginas
2. **SchemeFacade**: Gestión del esquema seleccionado
3. **CheckoutInfoFacade**: Datos del checkout
4. **UiCoreFacade**: Estado de UI (loading, etc.)

## Puntos de Integración Identificados

### 1. Query Parameters
- **Actualmente**: No implementado
- **Necesario**: Extracción de `installments` y `offerId` de URL
- **Ubicación**: `ngOnInit()` del PaymentOrchestratorComponent

### 2. Scheme Matching
- **Actualmente**: Selección automática del primer esquema o esquema con ID -1
- **Necesario**: Matching por número de cuotas (`installments`)
- **Ubicación**: Antes de la decisión de flujo

### 3. Flow Decision Logic
- **Actualmente**: Basado en `hasDynamicInstallments()`
- **Necesario**: Considerar query parameters para bypass de UI
- **Ubicación**: Líneas 67-72 del componente

## Dependencias Críticas

### Servicios Angular
- `ActivatedRoute`: Para acceso a query parameters
- `Router`: Para navegación

### Facades NgRx
- `SchemeFacade.setInfoCheckout()`: Para establecer esquema preseleccionado
- `PagesFacade.setCurrentPage()`: Para navegación

### Interfaces
- `IScheme`: Estructura de esquemas de pago
- `QueryParams`: Parámetros de consulta (ya definida)

## Patrones de Manejo de Errores

### Error Handling Actual
- `ErrorsHandlerService.errorHandler()`: Manejo centralizado de errores
- Fallback a comportamiento actual cuando hay errores
- Validación de token antes de procesar

### Patrón Recomendado para Query Params
- Validación de parámetros antes de procesar
- Fallback a lógica actual si parámetros son inválidos
- Logging para debugging

## Estado Actual vs Requerimientos

### ✅ Ya Implementado
- Estructura base del componente
- Lógica de decisión de flujo
- Manejo de esquemas
- Facades y servicios necesarios
- Interface `QueryParams` definida

### ❌ Falta Implementar
- Extracción de query parameters
- Validación de parámetros
- Scheme matching por installments
- Lógica de preselection
- Manejo de offerId

## Arquitectura de Testing

### Archivos de Test Existentes
- `payment-orchestrator.component.spec.ts`: Tests unitarios del componente
- Estructura de testing con Jasmine + Karma

### Estrategia de Testing Recomendada
- Tests para extracción de query parameters
- Tests para scheme matching
- Tests para fallback behavior
- Tests de integración con parámetros reales

## Consideraciones de Performance

### Impacto Mínimo Esperado
- Procesamiento adicional: <50ms
- Sin cambios en APIs externas
- Sin cambios en estructura de datos
- Solo lógica adicional en componente existente

### Optimizaciones Identificadas
- Validación temprana de parámetros
- Caching de esquemas para matching
- Logging estratégico para debugging
