# Puntos de Integración - Partner Payments Library

## Integración con Sistemas Externos

### 1. Client-Walmart Integration

#### URL Format Esperado
```
/payment?installments=6&offerId=123
```

#### Parámetros de Integración
- **installments**: Número de cuotas deseado (string)
- **offerId**: Identificador de oferta (string, opcional)

#### Flujo de Integración
```
Client-Walmart → URL con Query Params → Payment Orchestrator → Scheme Preselection → Plan Confirmation
```

### 2. API Integration Points

#### CheckoutInfoFacade
```typescript
// Endpoint: GET /checkout/info
// Response: ICheckoutInfo con array de schemes
getInfoCheckout(token: string): void {
  // Obtiene información completa del checkout incluyendo schemes
}
```

#### Scheme Data Flow
```
API → CheckoutInfoFacade → checkoutInfo$ → Schemes Array → Scheme Matching → Scheme Selection
```

## Puntos de Integración Internos

### 1. Angular Router Integration

#### ActivatedRoute Service
```typescript
// Acceso a query parameters
this._route.queryParams.subscribe(params => {
  // params.installments, params.offerId
});
```

#### Router Navigation
```typescript
// Navegación entre páginas
this._router.navigate(['/payment'], { 
  queryParams: { installments: '6', offerId: '123' } 
});
```

### 2. NgRx Store Integration

#### SchemeFacade
```typescript
// Establecer esquema seleccionado
setInfoCheckout(scheme: IScheme): void {
  this.store.dispatch(schemeActions.setScheme({ scheme }));
}

// Obtener esquema seleccionado
getSchemeSelected$ = this.store.pipe(select(schemeSelector.schemeSelector));
```

#### PagesFacade
```typescript
// Control de navegación entre páginas
setCurrentPage(page: Pages): void {
  // Implementación de navegación basada en enum Pages
}
```

### 3. Service Integration

#### CheckoutInfoFacade
```typescript
// Observable de información de checkout
checkoutInfo$: Observable<ICheckoutInfo>;

// Método para refrescar datos
refreshCheckout$(): void;
```

#### ErrorsHandlerService
```typescript
// Manejo centralizado de errores
errorHandler(error: any): void {
  // Procesamiento de errores y navegación a páginas de error
}
```

## Interfaces de Integración

### 1. Query Parameters Interface
```typescript
interface QueryParams {
  installments?: string;        // Número de cuotas como string
  offerId?: string;            // ID de oferta como string
}
```

### 2. Scheme Interface
```typescript
interface IScheme {
  id: number;                  // ID único del esquema
  installments: number;         // Número de cuotas (CRÍTICO para matching)
  commission: number;           // Comisión
  fee_amount: number;          // Monto de cuota
  total: number;               // Total a pagar
  total_first_installment: number; // Primera cuota
  calendar: string[];          // Calendario de pagos
  customer_discounts_applied: IDiscount[]; // Descuentos aplicados
  aplazo_points_amounts?: {   // Montos con Aplazo Points
    aplazo_points_used: number;
    commission: number;
    fee_amount: number;
    // ... otros campos
  };
}
```

### 3. Checkout Info Interface
```typescript
interface ICheckoutInfo {
  id: number;                  // ID del checkout
  merchant_id: number;          // ID del merchant
  purchase_amount: number;      // Monto de compra
  schemes: IScheme[];          // Array de esquemas disponibles
  next_quincena_enabled: boolean; // Habilitación de próxima quincena
  promo_code_enabled: boolean;  // Habilitación de códigos promocionales
  customer_type?: 'BNPL' | 'PAY_NOW'; // Tipo de cliente
  source_type: string;         // Tipo de fuente (ej: 'VC' para virtual card)
  error?: any;                 // Error si existe
  // ... otros campos
}
```

## Flujo de Integración Completo

### 1. Entrada Externa
```
Client-Walmart → URL: /payment?installments=6&offerId=123
```

### 2. Procesamiento Interno
```
Payment Orchestrator → Extract Query Params → Validate Params → Load Checkout Info → Match Scheme → Set Selected Scheme → Navigate to Plan Confirmation
```

### 3. Salida al Usuario
```
Plan Confirmation Page → Pre-selected Scheme → Ready for Payment
```

## Validación de Integración

### 1. Validación de Parámetros
```typescript
private validateQueryParams(params: QueryParams): {
  isValid: boolean;
  installments?: number;
  offerId?: string;
  errors: string[];
} {
  const errors: string[] = [];
  let installments: number | undefined;
  
  if (params.installments) {
    const parsed = parseInt(params.installments, 10);
    if (isNaN(parsed) || parsed <= 0) {
      errors.push('Invalid installments parameter');
    } else {
      installments = parsed;
    }
  }
  
  return {
    isValid: errors.length === 0,
    installments,
    offerId: params.offerId,
    errors
  };
}
```

### 2. Validación de Esquemas
```typescript
private validateSchemesForMatching(schemes: IScheme[], installments: number): {
  isValid: boolean;
  matchingScheme?: IScheme;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!Array.isArray(schemes) || schemes.length === 0) {
    errors.push('No schemes available');
    return { isValid: false, errors };
  }
  
  const matchingScheme = schemes.find(scheme => scheme.installments === installments);
  
  if (!matchingScheme) {
    errors.push(`No scheme found for ${installments} installments`);
  }
  
  return {
    isValid: errors.length === 0,
    matchingScheme,
    errors
  };
}
```

## Manejo de Errores de Integración

### 1. Errores de Parámetros
- **Parámetro inválido**: Fallback a lógica actual
- **Parámetro faltante**: Usar comportamiento por defecto
- **Parámetro malformado**: Log error y continuar

### 2. Errores de Matching
- **No matching scheme**: Fallback a lógica actual
- **Múltiples matches**: Seleccionar por criterio definido
- **Esquemas vacíos**: Error handling estándar

### 3. Errores de API
- **Checkout info error**: Manejo existente
- **Token inválido**: Redirección a not-found
- **Network errors**: Error handling estándar

## Logging de Integración

### 1. Información de Entrada
```typescript
private logIntegrationStart(params: QueryParams): void {
  console.log('Integration Start:', {
    timestamp: new Date().toISOString(),
    source: 'external',
    parameters: {
      installments: params.installments,
      offerId: params.offerId
    }
  });
}
```

### 2. Información de Procesamiento
```typescript
private logSchemeMatching(installments: number, schemes: IScheme[], result: IScheme | null): void {
  console.log('Scheme Matching:', {
    requestedInstallments: installments,
    availableSchemes: schemes.length,
    availableInstallments: schemes.map(s => s.installments),
    matchedScheme: result?.id || 'none',
    fallbackUsed: result === null
  });
}
```

### 3. Información de Salida
```typescript
private logIntegrationComplete(success: boolean, selectedScheme?: IScheme): void {
  console.log('Integration Complete:', {
    success,
    selectedScheme: selectedScheme?.id,
    navigationTarget: success ? 'planConfirmation' : 'fallback'
  });
}
```

## Testing de Integración

### 1. Test Cases de Integración Externa
```typescript
describe('External Integration', () => {
  it('should handle valid installments parameter', () => {
    // Test con parámetro válido
  });
  
  it('should handle invalid installments parameter', () => {
    // Test con parámetro inválido
  });
  
  it('should handle missing parameters', () => {
    // Test sin parámetros
  });
});
```

### 2. Test Cases de Matching
```typescript
describe('Scheme Matching', () => {
  it('should find matching scheme', () => {
    // Test de matching exitoso
  });
  
  it('should handle no matching scheme', () => {
    // Test de fallback
  });
  
  it('should handle multiple matching schemes', () => {
    // Test de selección por criterio
  });
});
```

## Consideraciones de Performance

### 1. Impacto en Tiempo de Carga
- **Procesamiento adicional**: <50ms
- **Validación de parámetros**: <10ms
- **Scheme matching**: <20ms
- **Logging**: <5ms

### 2. Optimizaciones
- **Cache de esquemas**: Evitar re-procesamiento
- **Validación temprana**: Falla rápida en casos inválidos
- **Logging condicional**: Solo en desarrollo

### 3. Monitoreo
- **Métricas de integración**: Success rate, fallback rate
- **Performance metrics**: Tiempo de procesamiento
- **Error tracking**: Errores de integración

## Compatibilidad y Backward Compatibility

### 1. URLs Sin Parámetros
- **Comportamiento**: Usar lógica actual
- **Testing**: Verificar que no hay regresión

### 2. URLs Con Parámetros Inválidos
- **Comportamiento**: Fallback a lógica actual
- **Logging**: Registrar intento de integración fallida

### 3. URLs Con Parámetros Válidos
- **Comportamiento**: Nueva funcionalidad de preselection
- **Testing**: Verificar funcionalidad completa
