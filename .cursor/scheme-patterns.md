# Análisis de Patrones de Esquemas - Partner Payments

## Estructura de Esquemas (IScheme)

### Campos Clave Identificados
```typescript
interface IScheme {
  id: number;                    // Identificador único del esquema
  installments: number;          // ⭐ Número de cuotas (CRÍTICO para matching)
  // Campos financieros
  commission: number;
  fee_amount: number;
  total: number;
  total_first_installment: number;
  // Campos de calendario
  calendar: string[];
  // Campos de descuentos
  customer_discounts_applied: IDiscount[];
  // Campos de Aplazo Points
  aplazo_points_amounts?: {
    aplazo_points_used: number;
    commission: number;
    fee_amount: number;
    // ... otros campos financieros específicos de Aplazo Points
  };
}
```

## Patrones de Selección Actuales

### 1. Selección Automática (Sin Cuotas Dinámicas)
```typescript
// Ubicación: payment-orchestrator.component.ts línea 70-71
this._pagesFacade.setCurrentPage(Pages.planConfirmation);
this._schemeFacade.setInfoCheckout(data.schemes[0]); // Siempre el primer esquema
```

### 2. Selección por Usuario (Con Cuotas Dinámicas)
```typescript
// Ubicación: select-installment.component.ts línea 74-78
private _setDefaultScheme(): void {
  this.schemeSelected = this.schemes.find(
    ({ installments }) => this._defaultInstallments === installments
  ) ?? this.schemes[0];
  this.formGroup.get(this.SCHEME_SELECTED).setValue(this.schemeSelected);
}
```

### 3. Validación de Cuotas Dinámicas
```typescript
// Ubicación: payment-orchestrator.component.ts línea 82-84
private hasDynamicInstallments(schemes: IScheme[]): boolean {
  return !schemes.some(scheme => scheme.id === -1) && schemes.length > 1;
}
```

## Patrones de Matching Identificados

### 1. Matching por ID Especial (-1)
- **Propósito**: Esquema especial que indica "sin cuotas dinámicas"
- **Uso**: Cuando `scheme.id === -1`, se considera que no hay selección dinámica
- **Comportamiento**: Va directo a `planConfirmation` con el primer esquema

### 2. Matching por Número de Cuotas
- **Propósito**: Selección basada en `installments`
- **Uso**: En `select-installment` para encontrar esquema por cuotas específicas
- **Comportamiento**: Busca esquema con `installments` específico

### 3. Matching por Posición (Primer Esquema)
- **Propósito**: Selección automática del primer esquema disponible
- **Uso**: Cuando no hay cuotas dinámicas o como fallback
- **Comportamiento**: Usa `schemes[0]`

## Flujo de Datos de Esquemas

### 1. Origen de Datos
```
API Response → CheckoutInfoFacade → checkoutInfo$ → Schemes Array
```

### 2. Procesamiento Actual
```
Schemes → hasDynamicInstallments() → Decision Logic → Scheme Selection
```

### 3. Almacenamiento
```
Selected Scheme → SchemeFacade.setInfoCheckout() → NgRx Store → schemeSelector
```

## Casos Edge Identificados

### 1. Esquemas Vacíos
- **Escenario**: `schemes.length === 0`
- **Comportamiento Actual**: Error handling
- **Consideración**: Validar antes de matching

### 2. Esquemas con Mismo Número de Cuotas
- **Escenario**: Múltiples esquemas con `installments` idéntico
- **Comportamiento Actual**: Toma el primero encontrado
- **Consideración**: Definir criterio de selección (ej: menor ID)

### 3. Esquemas con ID -1
- **Escenario**: Esquema especial presente
- **Comportamiento Actual**: Desactiva cuotas dinámicas
- **Consideración**: Mantener lógica existente

### 4. Parámetros Inválidos
- **Escenario**: `installments` no numérico o fuera de rango
- **Comportamiento Actual**: No implementado
- **Consideración**: Fallback a lógica actual

## Patrones de Validación Recomendados

### 1. Validación de Parámetros
```typescript
private validateInstallmentsParam(value: string): number | null {
  const parsed = parseInt(value, 10);
  return (!isNaN(parsed) && parsed > 0) ? parsed : null;
}
```

### 2. Validación de Esquemas
```typescript
private validateSchemes(schemes: IScheme[]): boolean {
  return Array.isArray(schemes) && schemes.length > 0;
}
```

### 3. Matching Robusto
```typescript
private findSchemeByInstallments(schemes: IScheme[], installments: number): IScheme | null {
  if (!this.validateSchemes(schemes)) return null;
  
  const matchingSchemes = schemes.filter(scheme => scheme.installments === installments);
  
  if (matchingSchemes.length === 0) return null;
  if (matchingSchemes.length === 1) return matchingSchemes[0];
  
  // Múltiples matches: seleccionar por menor ID
  return matchingSchemes.reduce((prev, current) => 
    prev.id < current.id ? prev : current
  );
}
```

## Integración con Facades

### SchemeFacade
```typescript
// Método existente para establecer esquema
setInfoCheckout(scheme: IScheme): void {
  this.store.dispatch(schemeActions.setScheme({ scheme }));
}

// Observable para obtener esquema seleccionado
getSchemeSelected$ = this.store.pipe(select(schemeSelector.schemeSelector));
```

### PagesFacade
```typescript
// Método para navegación
setCurrentPage(page: Pages): void {
  // Implementación de navegación
}
```

## Consideraciones de Performance

### 1. Caching de Esquemas
- **Actual**: No implementado
- **Recomendación**: Cache en componente para múltiples operaciones
- **Beneficio**: Evitar re-procesamiento de arrays grandes

### 2. Optimización de Búsqueda
- **Actual**: `Array.find()` para cada búsqueda
- **Recomendación**: Crear Map por installments si hay muchos esquemas
- **Beneficio**: O(1) lookup vs O(n)

### 3. Validación Temprana
- **Actual**: Validación en cada operación
- **Recomendación**: Validar una vez y cachear resultado
- **Beneficio**: Menos procesamiento repetitivo

## Logging y Debugging

### Información Crítica a Loggear
1. **Parámetros recibidos**: `installments`, `offerId`
2. **Esquemas disponibles**: Cantidad y range de cuotas
3. **Matching results**: Esquema encontrado o fallback usado
4. **Decision logic**: Por qué se tomó cada decisión

### Estrategia de Logging
```typescript
private logSchemeMatching(installments: number, schemes: IScheme[], result: IScheme | null): void {
  console.log('Scheme Matching:', {
    requestedInstallments: installments,
    availableSchemes: schemes.length,
    availableInstallments: schemes.map(s => s.installments),
    matchedScheme: result?.id || 'none',
    fallbackUsed: result === null
  });
}
```

## Compatibilidad con Comportamiento Actual

### Mantener Funcionalidad Existente
1. **hasDynamicInstallments()**: Sin cambios
2. **Selección automática**: Como fallback
3. **Error handling**: Mismo patrón
4. **Navegación**: Misma lógica

### Nuevas Funcionalidades
1. **Query parameter extraction**: Nueva funcionalidad
2. **Scheme matching**: Nueva funcionalidad
3. **Preselection logic**: Nueva funcionalidad
4. **Enhanced logging**: Nueva funcionalidad

## Testing Strategy

### Test Cases Críticos
1. **Valid installments parameter**: Debe encontrar esquema correcto
2. **Invalid installments parameter**: Debe usar fallback
3. **No matching scheme**: Debe usar fallback
4. **Multiple matching schemes**: Debe seleccionar por menor ID
5. **Empty schemes array**: Debe manejar gracefully
6. **Missing query parameters**: Debe usar lógica actual

### Mock Data para Testing
```typescript
const mockSchemes: IScheme[] = [
  { id: 1, installments: 3, /* ... */ },
  { id: 2, installments: 6, /* ... */ },
  { id: 3, installments: 12, /* ... */ },
  { id: -1, installments: 1, /* ... */ } // Esquema especial
];
```
