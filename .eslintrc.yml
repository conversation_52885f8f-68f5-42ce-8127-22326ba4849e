root: true
env:
  es6: true
  node: true
  jest: true
plugins:
  - promise
  - "@typescript-eslint"
extends:
  - eslint:recommended
  - plugin:@typescript-eslint/eslint-recommended
  - plugin:@typescript-eslint/recommended
  - plugin:@typescript-eslint/recommended-requiring-type-checking
  - plugin:promise/recommended
parser: "@typescript-eslint/parser"
parserOptions:
  tsconfigRootDir: .
  project:
    - ./tsconfig.eslint.json
    - ./*/tsconfig.json
rules:
  "@typescript-eslint/ban-types": error # to allow "{}" as a type
  "@typescript-eslint/explicit-function-return-type": error
  "@typescript-eslint/interface-name-prefix": 0 # interfaces prefixed with "I" are perfectly fine
  "@typescript-eslint/no-floating-promises": warn
  "@typescript-eslint/no-inferrable-types": error
  "@typescript-eslint/no-unused-expressions": [error, { allowShortCircuit: true, allowTernary: true }]
  "@typescript-eslint/no-unused-vars": [error, { vars: all, ignoreRestSiblings: true }]
  "@typescript-eslint/no-useless-constructor": error
  "@typescript-eslint/no-var-requires": 0 # allow `require()`
  "@typescript-eslint/require-await": error
  no-console: error
  no-restricted-imports:
    - error
    - paths:
        - name: moment
          message: Use date-fns instead!
        - name: underscore
          message: Use lodash instead!
        - name: bluebird
          message: Use native Promises and async/await instead!
  import/order: 0 # we use prettier import sorting by module
  max-params:
    - 1
    - max: 7
  object-curly-spacing:
    - 2
    - always
  arrow-spacing:
    - 2
    - before: true
      after: true
  keyword-spacing:
    - 2
    - before: true
      after: true
  space-before-blocks:
    - 2
    - functions: always
      keywords: always
      classes: always
  space-before-function-paren:
    - 2
    - anonymous: always
      named: never
      asyncArrow: always
  no-trailing-spaces: 2
# overrides:
#   - files: ["*.js"]
#     rules:
#       "@typescript-eslint/no-floating-promises": off # avoid linter errors with .js files
