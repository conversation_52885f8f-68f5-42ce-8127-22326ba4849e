# Alternative Offers Implementation

## Overview
Se implementó la funcionalidad para manejar `alternative_offers` en el PaymentOrchestratorComponent, permitiendo que cuando se reciban query parameters con `offerId` e `installments`, el sistema pueda preseleccionar un esquema de pago desde los alternative offers en lugar de solo buscar en los esquemas originales.

## Cambios Realizados

### 1. Interfaces Actualizadas (`checkout-info.interface.ts`)

Se agregaron nuevas interfaces para manejar la estructura del response:

```typescript
export interface IOriginalOffer {
  offer_id: string;
  loan_amount: number;
  schemes: IScheme[];
}

export interface IAlternativeOffer {
  offer_id: string;
  loan_amount: number;
  installments: number;
  iva_amount_plus_commission_amount: number;
  fee_amount: number;
  total: number;
}
```

Y se actualizó `ICheckoutInfo` para incluir:
```typescript
original_offer?: IOriginalOffer;
alternative_offers?: IAlternativeOffer[];
```

### 2. PaymentOrchestratorComponent - Nueva Lógica

#### Flujo de Decisión Actualizado:

1. **Verificación de Alternative Offers (NUEVA)**:
   ```typescript
   if (queryInstallments && queryOfferId) {
     const alternativeScheme = this.getSchemeFromAlternativeOffer(data, queryOfferId, queryInstallments);
     
     if (alternativeScheme) {
       this._pagesFacade.setCurrentPage(Pages.planConfirmation);
       this._schemeFacade.setInfoCheckout(alternativeScheme);
       return;
     }
   }
   ```

2. **Fallback a esquemas originales**:
   ```typescript
   if (queryInstallments) {
     const matchedScheme = this.getSchemeByInstallments(data.schemes, queryInstallments);
     // ... resto de la lógica existente
   }
   ```

#### Nuevos Métodos:

##### `getSchemeFromAlternativeOffer()`
- Busca en `data.alternative_offers` un offer que coincida con `offerId` e `installments`
- Convierte el alternative offer a formato `IScheme`
- Retorna `null` si no encuentra coincidencia

##### `convertAlternativeOfferToScheme()`
- Convierte un `IAlternativeOffer` a formato `IScheme`
- Asigna `id: -2` para identificar esquemas de alternative offers
- Mapea los campos disponibles y asigna valores por defecto para campos faltantes

## Casos de Uso

### Caso 1: Alternative Offer Válido
```
URL: /walmart-cashi-payment?offerId=55e48097-d7d4-4832-91fa-2e9b848a6e97&installments=5

Response:
{
  "original_offer": {
    "schemes": [{ "installments": 3 }]
  },
  "alternative_offers": [
    {
      "offer_id": "55e48097-d7d4-4832-91fa-2e9b848a6e97",
      "installments": 5,
      "fee_amount": 1993.83,
      "total": 9969.12
    }
  ]
}

Resultado: → PlanConfirmationComponent (con alternative offer preseleccionado)
```

### Caso 2: Alternative Offer No Encontrado
```
URL: /walmart-cashi-payment?offerId=invalid-id&installments=5

Resultado: → Fallback a lógica original (buscar en schemes originales)
```

### Caso 3: Sin Query Parameters
```
URL: /walmart-cashi-payment

Resultado: → SelectInstallmentComponent (si hay múltiples esquemas)
```

## Estructura del Esquema Convertido

Cuando se convierte un `IAlternativeOffer` a `IScheme`:

```typescript
{
  id: -2,                    // ID especial para alternative offers
  installments: 5,           // Del alternative offer
  fee_amount: 1993.83,       // Del alternative offer
  total: 9969.12,           // Del alternative offer
  commission: 0,            // Valor por defecto
  interest_amount: 0,       // Valor por defecto
  total_first_installment: 1993.83,  // Igual a fee_amount
  first_installment: 1993.83,        // Igual a fee_amount
  iva_amount_plus_commission_amount: 2444.12,  // Del alternative offer
  calendar: [],             // Array vacío
  customer_discounts_applied: [],     // Array vacío
  // ... otros campos con valores por defecto
}
```

## Testing

Para probar la funcionalidad:

1. **Construir la librería**: `npm run build`
2. **Relinkar en el proyecto principal** (si es necesario)
3. **Probar con URL**: `http://localhost:4200/walmart-cashi-payment?offerId=55e48097-d7d4-4832-91fa-2e9b848a6e97&installments=5`

## Backward Compatibility

- ✅ **Mantiene compatibilidad total** con la lógica existente
- ✅ **Solo se activa** cuando hay `queryInstallments` Y `queryOfferId`
- ✅ **Fallback automático** a esquemas originales si no encuentra alternative offer
- ✅ **No afecta** flujos existentes sin alternative offers

## Next Steps

1. Probar en el proyecto principal con datos reales
2. Verificar que el PlanConfirmationComponent maneja correctamente esquemas con `id: -2`
3. Considerar agregar logs temporales para debugging si es necesario
