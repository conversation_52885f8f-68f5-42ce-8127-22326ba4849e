# Change Log

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

## [7.2.16]

### Changed

- [BE-1789](https://aplazo.atlassian.net/browse/BE-1789) avoid codi or update card when purchase is zero and user select codi payment

## [7.2.15-RELEASE] - 2025-04-03

### Changed

- update `@aplazo/client-virtual-card` to `~3.2.6`.
- update `partner-core` to `3.1.7`
- [BE-1533](https://aplazo.atlassian.net/browse/BE-1533) fix undefined error when try to add card
- [BE-1530](https://aplazo.atlassian.net/browse/BE-1530) Modify checkout amount for first installment, now is `total_first_installment` instead `feeAmount`
- [AQ-52](https://aplazo.atlassian.net/browse/AQ-52) Modify logos for information in checkout page
- Add feature flag to control modal for flexi checkout

## [7.2.14-RELEASE] - 2025-12-24

### Changed

- [BE-1645](https://aplazo.atlassian.net/browse/BE-1645) Add IVA to checkout page

## [7.2.13-RELEASE] - 2024-12-17

### Changed

- update card identifier service.
- update update cvv service

## [7.2.11-RELEASE] - 2024-11-21

### Changed

- upgrade `partner-analytics` to `~3.2.0`.

## [7.2.10-RELEASE] - 2024-11-19

### Changed

- add new function to manage error 4403

## [7.2.9-RELEASE] - 2024-11-07

### Changed

- [BE-518](https://aplazo.atlassian.net/browse/BE-518) update redirect to create VC
- [BE-488](https://aplazo.atlassian.net/browse/BE-488) - Checkout Adjust for checkout 100% off
- [BE-9056](https://aplazo.atlassian.net/browse/BE-906) - Add new validations to hide card method payment based on DI and ff treatment
- [BE-1085](https://aplazo.atlassian.net/browse/BE-1085) - update service for to update cvv
- [BE-1087](https://aplazo.atlassian.net/browse/BE-1087) - add new animation when user use aplazo points in thank you page

### Fixed

- [AC-142](https://aplazo.atlassian.net/browse/AC-142) - remove call to `ErrorsService.paymentReset()` on `ngOnDestroy` hook for `ErrorServerComponent`.

## [7.2.8-RELEASE] - 2024-10-29

### Changed

- update `@aplazo/front-feature-flags` version to `1.29.4`.

## [7.2.7-RELEASE] - 2024-10-29

### Changed

- [BE-1104](https://aplazo.atlassian.net/browse/BE-1104) - WEB disabled button confirm payment when payment methods is empty

## [7.2.6-RELEASE] - 2024-08-13

### Changed

- [BE-354](https://aplazo.atlassian.net/browse/BE-354) - Checkout update for promo Aplazo points (cashback)

## [7.2.5-RELEASE] - 2024-08-12

### Changed

- [AC-429](https://aplazo.atlassian.net/browse/AC-429) - upgrade `@aplazo/partner-analytics` to `~3.1.0`.

## [7.2.4-RELEASE] - 2024-07-25

### Changed

- [BE-342](https://aplazo.atlassian.net/browse/BE-342) - Hide payment method card for paynow users

## [7.2.3-RELEASE] - 2024-07-22

### Changed

- [BE-245](https://aplazo.atlassian.net/browse/BE-245) - CoDi update for paynow

## [7.2.2-RELEASE] - 2024-07-18

### Changed

- update `@aplazo/client-verification` to `~5.4.0`.

## [7.2.1-RELEASE] 2024-02-07

### Changed

- set userAplazoPoints false default
- fix add card with codi
- fix error to pay with codi with bank errors
- fix button submit disabled when user try to pay with codi

## [7.2.0-RELEASE] 2024-06-06

### Added

- [BE-128](https://aplazo.atlassian.net/browse/BE-128) - CoDi implementation
- [B2C-3487](https://aplazo.atlassian.net/browse/B2C-3487) - [Front] Create "4600" error screen.

### Changed

- [B2C-3458](https://aplazo.atlassian.net/browse/B2C-3458) - modify redirect WOO integration type to WOO confirmURL
- [BE-55](https://aplazo.atlassian.net/browse/BE-55) change `getCustomer()` for `me` store

### Removed

- `plan-confirmation-service`
- `customer-service`

## [7.1.2-RELEASE] - 2024-05-27

### Changed

- update `@aplazo/client-verification` to `~5.3.0`.

## [7.1.1-RELEASE] - 2024-05-14

### Hotfixed

- modify accordion to be opened by default

## [7.1.0-RELEASE] - 2024-05-06

- update `@aplazo/client-verification` to `~5.2.0`.
- update `@aplazo/client-virtual-card` to `~3.2.0`.
- update `@aplazo/partner-core` to `~3.1.0`.
- update `@aplazo/web-ui` to `~4.1.0`.

## [7.0.7-RELEASE] - 2024-04-25

- fix error in error payments page
- fix add card service, lambda for \_exceptionBins

## [7.0.6-RELEASE] - 2024-04-22

- update `@aplazo/client-verification` to `~5.1.0`.

## [7.0.5-RELEASE] - 2024-04-01

- update `@aplazo/client-virtual-card` to `~3.1.0`.
- [B2C-3259](https://aplazo.atlassian.net/browse/B2C-3259) - add stepper in checkout page for VC flow

## [7.0.4-RELEASE] - 2024-03-26

### Changed

- Update `@aplazo/front-feature-flags` to `1.29.0`

## [7.0.3-RELEASE] - 2024-03-13

### Hotfixed

- Modify logged attributes for ATO feature flag, adding `hourOfDay` and change `merchantId` for `merchantsId`

## [7.0.2-RELEASE] - 2024-03-12

### Changed

- [B2C-3105](https://aplazo.atlassian.net/browse/B2C-3105) - Create flag to enable or disable payment methods
- [B2C-3202](https://aplazo.atlassian.net/browse/B2C-3202) - Fix add cards for brands in card identifier
- [B2C-3297](https://aplazo.atlassian.net/browse/B2C-3297) - Create modal to verify date ATO
- [B2C-3299](https://aplazo.atlassian.net/browse/B2C-3299) - Create error modal error 4701
- [B2C-3304](https://aplazo.atlassian.net/browse/B2C-3304) - Create error modal error 4702

## [7.0.1-RELEASE] - 2024-05-03

### HOTFIXED

- [B2C-3210](https://aplazo.atlassian.net/browse/B2C-3210) - Adjust kustomer button position (mofidy fixed button)

## [7.0.0-RELEASE] - 2023-12-19

### Changed

- Update to Angular v15
- [B2C-2867](https://aplazo.atlassian.net/browse/B2C-2867) - Add new logic to modal error "Miswritten code"

## [6.1.0-RELEASE] 13-nov-2023

### Changed

- update `@aplazo/client-verification` to `~4.4.0`.
- [B2C-2676](https://aplazo.atlassian.net/browse/B2C-2676) - [BNPL] Revamp UI of success purchase screen for checkout
- [B2C-2774](https://aplazo.atlassian.net/browse/B2C-2774) - [BNPL] Update dynamic installments screens
- [B2C-2846](https://aplazo.atlassian.net/browse/B2C-2849) - Create Aplazo puntos checkbox
- [B2C-2873](https://aplazo.atlassian.net/browse/B2C-2873) - Create cvv modal

### Added

- add `@aplazo/front-feature-flags` version `~0.1.0`

## [6.0.2-RELEASE] - 2023-10-10

### Changed

- update `@aplazo/partner-analytics` to version `2.3.0`.

## [6.0.1-RELEASE] 2023-09-12

### Changed

- [B2C-2696](https://aplazo.atlassian.net/browse/B2C-2696) - Integrate lambdas for `card-identifier` and `bin exceptions`

## [6.0.0-RELEASE] - 2023-08-21

### Changed

- [B2C-2182] (https://aplazo.atlassian.net/browse/B2C-2182) - Change the current payment endpoint in the checkouts (first-installment)
- [B2C-2316] (https://aplazo.atlassian.net/browse/B2C-2316) - Change the current payment endpoint in the checkouts (confirm-payment)
- [B2C-2544] (https://aplazo.atlassian.net/browse/B2C-2544) - Create error screen (code 4411)
- [B2C-2545] (https://aplazo.atlassian.net/browse/B2C-2545) - Create error screen (code 4412)
- [B2C-2546] (https://aplazo.atlassian.net/browse/B2C-2546) - Revamp error screen (code 417)
- [B2C-2558] (https://aplazo.atlassian.net/browse/B2C-2558) - Create error screen (code 4403)
- [B2C-2556] (https://aplazo.atlassian.net/browse/B2C-2556) - Create error screen (code 4401)
- Change payment-orchastator
- remove caret from `@kushki/js`
- remove `error-payment` module, was only used to error `4100` just change component in routing for `error-landingpage`
- hide button `X` to close in all error pages
- Add `VGS` integation to add or replace card

### Added

- add `@vgs/collect-js` version `~0.6.1`
- new errors `4500` and `4505` for `vgs`
- new error `409`
- add `success-modal`

### Removed

- `error-insufficient-credit-available` module
- `plan-confirmation-old` module
- `payment-link` module
- `molecules.module.ts` module (was empty)
- `card.effects.ts`
- `payment.service.ts`
- `plan-confirmation-old.service.ts`
- `kushki.service`
- `ebanx.service`
- `conekta.service`
- `payment.providers.service`

## [5.4.0-RELEASE] - 2023-07-25

### Changed

- update `@aplazo/partner-core` to `2.4.0`
- update `@aplazo/client-verification` to `4.3.0`
- update `@aplazo/client-virtual-card` to `2.8.0`

## [5.3.0-RELEASE] 2023-07-24

### Changed

- [B2C-2183](https://aplazo.atlassian.net/browse/B2C-2183)
  - add `client-verification` as dependency.
  - add new risk validation service before payment.

## [5.2.3-RELEASE] 05-07-2023

### Changed

- add validations to doesnt load `conekta` and `openpay` scripts for chrome extension

## [5.2.2-RELEASE] 13-06-2023

## Added

- [B2C-2301] (https://aplazo.atlassian.net/browse/B2C-2301) - [Pixel] Implement script for Mediamath_Purchase.

## [5.2.1-RELEASE] 17-05-2023

## Changed

- [B2C-2228] (https://aplazo.atlassian.net/browse/B2C-2228) - Always Show CVV input when card is digital
- fix height for modal `add-card`

## [5.2.0-RELEASE] 2023-05-11

## Changed

- [B2C-2134] (https://aplazo.atlassian.net/browse/B2C-2134) - [Front] Update promo codes copies
- [B2C-2087] (https://aplazo.atlassian.net/browse/B2C-2087) - [Front] Promo code field only uppercase
- [B2C-2072] (https://aplazo.atlassian.net/browse/B2C-2072) - [Front] Allow more than one coupon per checkout

## [5.1.0-RELEASE] 2023-05-03

## Changed

- Update `@aplazo/web-ui` to version `3.0.0`.
- Update `@aplazo/client-virtual-card` to version `2.7.0`.
- [B2C-2191] (https://aplazo.atlassian.net/browse/B2C-2191) - Remove prefill name from add/replace card modal

## [5.0.0-RELEASE] 2023-04-18

## Changed

- Export modal AddCardComponent for New dashboard
- Fix some bugs in modal for card name

## [4.3.6-RELEASE] 2023-04-13

## Added

- [B2C-1777] (https://aplazo.atlassian.net/browse/B2C-1777) - [Hide balance] Add to JSON restriction by merchant ID
- [B2C-2102] (https://aplazo.atlassian.net/browse/B2C-2102) - [Hide balance] Ajustes en pantalla

## [4.3.5-RELEASE] 2023-04-12

## Hotfixed

- [AP-2918] (https://aplazo.atlassian.net/browse/AP-2918) - VC can be used to pay a loan

## [4.3.4-RELEASE] 2023-03-07

## Hotfixed

- Fix branches
- Update `add-card-template` style
- [B2C-1834](https://aplazo.atlassian.net/browse/B2C-1834) - fix screen to add/replace card

## [4.3.3-RELEASE] 2023-03-03

## Changed

-[B2C-1779](https://aplazo.atlassian.net/browse/B2C-1779) - hide button to change installments when is only one available -[B2C-1792](https://aplazo.atlassian.net/browse/B2C-1792) - New screen for virtual card checkout

## [4.3.2-RELEASE] 2023-02-14

## Changed

-Change service to add/replace card

## Fixed

- [B2C-1603](https://aplazo.atlassian.net/browse/B2C-1603) - [Stage] No se visualiza reemplazo de tarjeta.

## [4.3.1-RELEASE] - 2023-02-08

## Changed

- Update `@aplazo/partner-analytics` to version `2.2.0`.

## [4.3.0-RELEASE] - 2023-01-31

## Changed

- Upgrade library of `@aplazo/client-virtual-card` in version `2.6.0`.
- Avoid credit card's tokenization with cokecta

## [4.2.1-RELEASE] - 2023-01-10

### Fixed

-[AP-2536](https://aplazo.atlassian.net/browse/AP-2536) - Botones de pantalla /no-credit-available no direccionan correctamente -[B2C-1453](https://aplazo.atlassian.net/browse/B2C-1453) - No se muestra pantalla de Pagos pendientes V1 y V2 -[AP-2403](https://aplazo.atlassian.net/browse/AP-2403) - account for PaynextQuincena

## [4.2.0-RELEASE] 2022-12-26

## Fixed

- [B2C-1062](https://aplazo.atlassian.net/browse/B2C-1062) - Descuadre en promos de voucherify con nombre largo.

## Added

- [B2C-1464] (https://aplazo.atlassian.net/browse/B2C-1464) - Card Hash implementation

## [4.1.4-RELEASE] - 2022-12-15

## Hotfixed

- Change variable `isDisabledSubmit` binding in `aplazo-payments-plan-confirmation` and `aplazo-payments-plan-confirmation-old`
- Fix `isDisabledSubmit` change loan.total to loan.purchaseAmount in calculation to disabled button to complete purchase

## [4.1.3-RELEASE] - 2022-12-07

## Fixed

- Change `this._environment.api.baseUrl` to `this._environment.api.baseUrl` at `card.service.ts`

## [4.1.2-RELEASE] - 2022-12-06

## Fixed

- Fix error at `card.service`

## [4.1.1 - RELEASE] - 2022-11-30

## Added

- [AP-2427](https://aplazo.atlassian.net/browse/AP-2427) - [Online] NRT & Kount Screens Changes

## Changed

- Able `custom-card` on stage to test

## [4.1.0 - RELEASE] - 2022-11-24

## Added

- [AP-2243](https://aplazo.atlassian.net/browse/AP-2243) - User level Feature flag for PayNextQuincena
- [AP-2403](https://aplazo.atlassian.net/browse/AP-2403) - Schema title for 1 installment when has active FF `PayNextQuincena`.
- [AP-2346](https://aplazo.atlassian.net/browse/AP-2403) - Track unsuccessful tokenization attempts

## Fixed

- Unable button to pay negative loans

## [4.0.3-RELEASE] - 2022-11-11

### Added

- [AP-2374](https://aplazo.atlassian.net/browse/AP-2374) - New error screen layout. Adding loan id in error code.

## [4.0.2-RELEASE] - 2022-11-09

## Added

- [AP-2167](https://aplazo.atlassian.net/browse/AP-2167) - [Dynamic Installments] Add 1 installment plan
- [AP-2286] (https://aplazo.atlassian.net/browse/AP-2286) - NRT & Kount Screens Changes
- [B2C-1171](https://aplazo.atlassian.net/browse/B2C-1171) - Add discount codes components to plan-confirmation-template

## Changed

- Upgrade library of `@aplazo/web-ui` in version `2.7.0`.
- Implement required methods for discount codes in plan confirmation pages

## [4.0.1 - RELEASE] - 2022-10-24

### Hotfixed

- Clear token is removed

## [4.0.0 - RELEASE] - 2022-10-24

### Added

- [`translationKeysMap`](projects/partner-payments/src/lib/checkout/helpers/translation-key.ts) - Helper to resolve the translation key for a given page

- [AP-1826](https://aplazo.atlassian.net/browse/AP-1826) - [Front] Dynamic Installments.
- [AP-2195](https://aplazo.atlassian.net/browse/AP-2195) - [Product Improvement] Payments -> Cambios pantallas "Payment Errors" (reduccion vol)
- [AP-2110](https://aplazo.atlassian.net/browse/AP-2110) - [Front Check-out] Swap payment-link-generation endpoints on Checkout

### Changed

- Payments refactor
- [AP-2053](https://aplazo.atlassian.net/browse/AP-2053) - Kushki library update. -[AP-2126](https://aplazo.atlassian.net/browse/AP-2126) - 403 error handling.

**New Flow**

- [`pagesEnum`](projects/partner-payments/src/lib/checkout/enums/pages.enum.ts) - Added _walmartCashi_ to describe a redirection route outside payments
- [`paymentCheckoutService`](projects/partner-payments/src/lib/checkout/services/payment-checkout/payment-checkout.service.ts) - Added validation to `paymentSuccessHandler` when `WM-Aplazo` integration type exists
- [`oldPlanConfirmationComponent`](projects/partner-payments/src/lib/checkout/pages/old/plan-confirmation-old/plan-confirmation-old.component.ts) - improve the getTranslationFlow method
- [`planConfirmationComponent`](projects/partner-payments/src/lib/checkout/pages/plan-confirmation/plan-confirmation.component.ts) - improve the getTranslationFlow method

## [3.8.0 - RELEASE] - 2022-10-11

## Added

- Upgrade library of `@aplazo/client-virtual-card` in version `2.5.0`.

## [3.7.0 - RELEASE] - 2022-10-06

### Changed

**Old Flow**

- [`paymentsComponent`](projects/partner-payments/src/lib/components/payments/payments.component.ts) - Added Walmart-Cashi redirection flow
- [`pagesEnum`](projects/partner-payments/src/lib/enums/pages.enum.ts) - Added walmart-cashi's configuration
- [`paymentFlows`](projects/partner-payments/src/lib/enums/payment-flows.ts) - Added walmart-cashi's configuration
- [`translateFacade`](projects/partner-payments/src/lib/store/facades/translate/translate.facade.ts) - Added walmart-cashi's configuration
- Upgrade library of `@aplazo/web-ui` in version `2.6.0`

## [3.6.0-RELEASE] - 2022-09-14

### Cahnged

- Upgrade library of `@aplazo/web-ui` in version `2.5.0`
- Upgrade library of `@aplazo/virtual-card` in version `2.4.0`.

## [3.5.3-RELEASE] - 2022-08-29

### Changed

- Upgrade library of `@aplazo/web-ui` in version `2.4.0`

## [3.5.2-RELEASE] - 2022-08-25

### Adding

- [AP-1773](https://aplazo.atlassian.net/browse/AP-1773) - [Front] Insufficient Funds Error (First Payment)

## [3.5.1-RELEASE] - 2022-08-16

### Fixing

- enabling Kount for online

## [3.5.0-RELEASE] - 2022-08-16

### Added

- [AP-1949](https://aplazo.atlassian.net/browse/AP-1949) - Rejected transaction screen Kount Offline

### Fixed

- [AP-1951](https://aplazo.atlassian.net/browse/AP-1951) - [Bloqueo Error 4100] Después de cerrar sesión e iniciar con otro login no se muestra el checkout.

## [3.4.4-RELEASE] - 2022-08-11

### Changed

- Upgrade library of `@aplazo/web-ui` in version `2.3.0`
- Upgrade library of `@aplazo/partner-core` in version `2.3.0`.

## [3.4.3-RELEASE] - 2022-08-09

### Fixed

- Removing Openpay

## [3.4.2-RELEASE] - 2022-08-05

### Fixed

- Bug - Customer can pay with down payment

## [3.4.1-RELEASE] - 2022-08-02

### Added

- [AP-1543](https://aplazo.atlassian.net/browse/AP-1543) - As a RAP, I want to generate the DataCollector in the front to complement the Kount implementation.

### Changed

- Change redirect to login method on logout button

## [3.4.0-RELEASE] - 2022-07-27

### Added

- [AP-1741](https://aplazo.atlassian.net/browse/AP-1741) - As a RAP, I want be capable to turn on and off the rule of pay next quincena (Front)
- [AP-1764](https://aplazo.atlassian.net/browse/AP-1764) - Change the url at Virtual Card payment plan confirmation.
- [B2C-813](https://aplazo.atlassian.net/browse/B2C-813) - Update GTM event at loan creation.

### Changed

- Upgrade library of `@aplazo/virtual-card` in version `2.3.0`.
- Upgrade library of `@aplazo/partner-core` in version `2.2.0`.
- Package.json command

### Fixed

- [B2C-842](https://aplazo.atlassian.net/browse/B2C-842) - Offline/online payNextQuincena no se visualiza el evento.

## [3.3.0-RELEASE] - 2022-07-14

### Changed

- Upgrade library of `@aplazo/partner-core` in version `2.1.0`.
- Upgrade library of `@aplazo/virtual-card` in version `2.2.1`.

## [3.2.0-RELEASE] - 2022-07-07

### Changed

- Upgrade library of `@aplazo/web-ui` in version `2.1.0`.
- Upgrade library of `@aplazo/virtual-card` in version `2.2.0`.
- Blocking btn of payment

## [3.1.0-RELEASE] - 2022-07-05

### Added

- [AP-1675](https://aplazo.atlassian.net/browse/AP-1675) - As a RAP, I want to validate the days past due on a customer's loan to decide if he can make purchases (Front).

### Changed

- [AP-1631](https://aplazo.atlassian.net/browse/AP-1631) - Mostrar siempre el campo del CVV.

## [3.0.0-RELEASE] - 2022-06-23

### IMPORTANT

- Upgrade Angular 10 -> 13

### Changed

- Upgrade library of `@aplazo/partner-core` in version `2.0.0`
- Upgrade library of `@aplazo/virtual-card` in version `2.0.0`
- Upgrade library of `@aplazo/web-ui` in version `2.0.0`

### Added

- Token Ebanx production only works for Debitcard
- [B2C-658](https://aplazo.atlassian.net/browse/B2C-658) - Quantities breakdown improvement, add 2 new parameters to plan-page
- [B2C-677](https://aplazo.atlassian.net/browse/B2C-658) - Checkout with extra installment screen redesign.
- [AP-1668](https://aplazo.atlassian.net/browse/AP-1668) - Cuando existe error de pago "417" se debe mantener desactivado el boton de "intentar de nuevo" en pantalla de error o en pantalla de confirmación de plan en caso de regresar hasta que se reemplace la tarjeta.

## [2.5.0-RELEASE] - 2022-06-09

### Changed

- Upgrade library of `@aplazo/web-ui` in version `1.11.0`
- Upgrade library of `@aplazo/virtual-card` in version `1.4.0`
- Upgrade library of `@aplazo/partner-core` in version `1.7.0`

## [2.4.0-RELEASE] - 2022-06-01

### Added

- [AP-1540](https://aplazo.atlassian.net/browse/AP-1540) - As a RAP, I want that purchases cannot be processed when the merchant has the flags payNextQuincena and firstInstallments active (Front).
- Ebanx payment links

## [2.3.1-RELEASE] - 2022-05-27

### Hotfix

- Fixing paymentlink response

## [2.3.0-RELEASE] - 2022-05-19

### Added

- [AP-1426](https://aplazo.atlassian.net/browse/AP-1426) - As Aplazo, I want to implement Ebanx as a new payment processor, so that to replace Conekta (Front)
- [AP-1499](https://aplazo.atlassian.net/browse/AP-1499) - As a RAP, I want to customize the WhatsApp links to support on error screens

### Changed

- Update `@aplazo/partner-core` to `1.6.0`
- Upgrade library of `@aplazo/web-ui` in version `1.10.0`
- Upgrade library of `@aplazo/virtual-card` in version `1.3.0`

### Fixed

- Show payment link options
- Change prefilled message (whatsapp) for KYC error

## [2.2.0-RELEASE] - 2022-05-12

- Upgrade library of `@aplazo/web-ui` in version `1.9.0`
- Upgrade library of `@aplazo/virtual-card` in version `1.2.0`

## [2.1.3-RELEASE] - 2022-05-09

### Changed

- Changing Virtual Card Copy.

## [2.1.2-RELEASE] - 2022-05-05

### Removed

- Cleaning loan token when payment is success.

## [2.1.1-RELEASE] - 2022-05-04

### Changed

- Update `@aplazo/client-virtual-card` to `1.1.1`

### Fixed

- Cleaning CVV input form after 5 min.
- Deleting double reducer for payLoanError.
- Hide loader when payLoan has an error.
- Cleaning loan token when payment is success.
- Removing cat_type_integration API for success page

## [2.1.0-RELEASE] - 2022-04-28

### Changed

- Update `@aplazo/partner-core` to `1.5.0`
- Update `@aplazo/client-virtual-card` to `1.1.0`
- Update `@aplazo/web-ui` to `1.8.0`
- [AP-1201](https://aplazo.atlassian.net/browse/B2C-601) Change WhatsApp prefilled message on new error screen

## [2.0.0-RELEASE] - 2022-04-25

### Added

- [AP-1201](https://aplazo.atlassian.net/browse/AP-1201) - As a Aplazo, I want to implement new screens for the Request Card flow in the front for Virtual Card
- [B2C-537](https://aplazo.atlassian.net/browse/B2C-537) - As a Aplazo, I want pre fill info add card
- [B2C-514](https://aplazo.atlassian.net/browse/B2C-514) - Change the generic error screen to error KYC
- [AP-1417](https://aplazo.atlassian.net/browse/AP-1417) - As a RAP, i want to create a new error screen to differentiate between "CANCELLED" from NRT and Fraud Rule (DOTO).
- New error message when customer can´t pay for 24 hours.

### Changed

- Payment Refactor
- Update `@aplazo/partner-core` to `1.4.1`
- Update `@aplazo/client-virtual-card` to `1.0.0`
- Update `@aplazo/web-ui` to `1.7.0`

### Removed

- Removing `lottie-web` dependency.
- Removing `ngx-lottie` dependency.

## [1.6.6-RELEASE] - 2022-04-19

### Hotfixed

- Add new message in case error 400 --> 4001 for HARD RULE

## [1.6.5-RELEASE] - 2022-04-18

### Hotfixed

- Add new message in case error 400 --> 4000 for NRT

## [1.6.4-RELEASE] - 2022-04-13

### Hotfixed

- Add new message in case error 406 for pay next fortnight
- update `lottie-web` to `5.7.11`
- update `ngx-lottie` to `5.4.0`

## [1.6.3-RELEASE] - 2022-04-04

### Changed

- Update `@aplazo/partner-core` to `1.4.0`
- Update `@aplazo/web-ui` to `1.6.0`

## [1.6.2-RELEASE] - 2022-04-01

### Added

- [AP-1315](https://aplazo.atlassian.net/browse/AP-1315) - As a RAP, I want that the credit limit to increase in the merchants selectedthe user can overdraw by x amount.

## [1.6.1-RELEASE] - 2022-03-29

### Changed

- Update `@aplazo/web-ui` to `1.5.1`

## [1.6.0-RELEASE] - 2022-03-28

### Added

- [AP-1148](https://aplazo.atlassian.net/browse/AP-1148) - As RAP, I want to create a new loan status (On Hold) so that I can manage certain online orders that match specific conditions.

### Changed

- Update `@aplazo/web-ui` to `1.5.0`

## [1.5.2-RELEASE] - 2022-03-15

### Changed

- Upgrating version of `@aplazo/partner-core` to `1.3.0`
- Update `@aplazo/web-ui` to `1.4.0`

### Fixed

- Changing the error handling of the Add card flow

## [1.5.1-RELEASE] - 2022-03-08

### Fixed

- Adding error message

## [1.5.0-RELEASE] - 2022-03-07

### Added

- Upgrating version of `@aplazo/partner-core` to `1.2.0`
- Update `@aplazo/web-ui` to `1.3.0`

### Changed

- Changing images and payment status

### Fixed

- Adding catchError in card service

## [1.4.0-RELEASE] - 2022-02-14

### Added

- tests for `Calendar-service`
- `mock/calendarResponse.dummy` file for run tests
- `mock/checkout-firs-installment-extra-amount.dummy` file for run tests
- `mock/checkout-normal.dummy` file for run tests
- `mock/partner-core-environment.mock` file for run tests
- closign session if card error is Bad user

### Changed

- Update `karma` version to `6.3.13` to fix log4js-node bugs within node_modules libraries.
- Update `@aplazo/web-ui` to `1.2.0`
- Update type for stepper variable within `confirmation-payment.component` to <IStep[]>

## [1.3.0-RELEASE] - 2022-02-03

### Changed

- [B2C-313](https://aplazo.atlassian.net/browse/B2C-313) Update @aplazo/web-ui to `1.1.0`
- [AP-199](https://aplazo.atlassian.net/browse/APM-199) Update @aplazo/partner-core to `1.1.0`
- [AP-1027](https://aplazo.atlassian.net/browse/AP-1027) As Aplazo, I want to send a message if the user is using a dynamic CVV asking them to upload again their card (Sprint 25).
- Replace ICheckout from local to partner-core source.
- Replace CatIntegrationTypeCode for PluginIntegration enum from partner-core
- Show detail discounts

## [1.2.0-RELEASE] - 2022-01-18

### Added

- [AP-931](https://aplazo.atlassian.net/browse/AP-931) FRONT - As Aplazo, I want to offer PayNextQuincena for First Time Users (Implementation).
- [AP-983](https://aplazo.atlassian.net/browse/AP-983) Property to Send "token" en kushki.
- [AP-1087](https://aplazo.atlassian.net/browse/AP-1087) As RAP, I want to not show the payment link in offline transactions if the error message is "insufficient funds".
- [B2C-307](https://aplazo.atlassian.net/browse/B2C-307) Improve page views analytics with GTM Datalayer.

## [1.1.1-RELEASE] - 2022-01-02

### Fixed

- Deleting conekta validators

## [1.1.0-RELEASE] - 2021-12-07

### Added

- [AP-879](https://aplazo.atlassian.net/browse/AP-879) As Aplazo, I want to identify if any order is offline and send the keys (Kushki)
- [AP-927](https://aplazo.atlassian.net/browse/AP-927) Defect: Check that the design of the black button matches the application.
- Add-card component add getter isButtonSubmitDisabled
- Upgrading version of @aplazo/web-ui to `1.0.10`

## [1.0.15-RELEASE] - 2021-12-02

### Changed

- [AP-902](https://aplazo.atlassian.net/browse/AP-902) Crear componente botoón con nuevos estilos Aplazo
- Upgrading version of @aplazo/web-ui to `1.0.9`
- [AP-882](https://aplazo.atlassian.net/browse/AP-882) As Aplazo, I want to disable payment link if the order is less than $100MXN.

## [1.0.14-RELEASE] - 2021-11-30

### Added

- [AP-882](https://aplazo.atlassian.net/browse/AP-882) As Aplazo, I want to disable payment link if the order is less than $250MXN.
- Upgrating version of `@aplazo/web-ui` to `1.0.8`
- Upgrating version of `@aplazo/partner-core` to `1.0.5`

## [1.0.13-RELEASE] - 2021-11-11

### Fixed

- The payment link is shown for all posui transaction errors.

## [1.0.12-RELEASE] - 2021-11-09

### Added

- [AP-853](https://aplazo.atlassian.net/browse/AP-853) Display informational text about digital cards.
- [AP-798](https://aplazo.atlassian.net/browse/AP-798) FRONT - As Aplazo, I want to show the payment link option at the checkout so the users will have more options to pay their first installment after an error with card

## [1.0.11-RELEASE] - 2021-11-03

### Added

- [APM-158](https://aplazo.atlassian.net/browse/APM-158) Condicionar el boton de regresar a los metodos de pago si es POSUI
- Pantalla de error cuando el pago es rechazado por una tienda de Shopify

### Fixed

- [AP-852](https://aplazo.atlassian.net/browse/AP-852) FRONT - As Aplazo, I want to create a feature flag for all the online loans, so we set the first payment as NEXT and generate the calendar later.
- First installment is active when loan is from POSUI.

## [1.0.10-RELEASE] - 2021-10-28

### Added

- [APM-131](https://aplazo.atlassian.net/browse/APM-131) As Aplazo, I want to be able to return to my oroginal cart in Shopify if my aplazo credit or payment is rejected.
- [AP-784](https://aplazo.atlassian.net/browse/AP-784) As Aplazo, I want to require the add a debit/credit card once there is an attempt of sale, after showing the payment plan so we can enable more payment options.
- [AP-795](https://aplazo.atlassian.net/browse/AP-795) As Aplazo, I want to complete the frontend dev for Openpay, so the app can be accepted.
- [AP-796](https://aplazo.atlassian.net/browse/AP-796) FRONT - As Aplazo, I want to create a feature flag for all the online loans, so we set the first payment as NEXT and generate the calendar later.
- Adding loader to the beginning of the flow.

### Fixed

- Getting feature flags when reloading page to avoid insufficient credit error.

## [1.0.9-RELEASE] - 2021-10-18

### Added

- Upgrating version of @aplazo/web-ui to 1.0.6
- Upgrating version of @aplaz0/partner-core to 1.0.3

## [1.0.8-RELEASE] - 2021-10-09

### Fixed

- Pantalla de First Intallment cuando el total es menor al crédito disponible pero el First extra amount es cero

## [1.0.7-RELEASE] - 2021-10-06

### Added

- [AP-689](https://aplazo.atlassian.net/browse/AP-689) As Aplazo, I want to implement first installment.
- [AP-741](https://aplazo.atlassian.net/browse/AP-741) Pruebas first installment.
- [AP-750](https://aplazo.atlassian.net/browse/AP-750) Copy al agregar la tarjeta de cobro de 0-10MXN
- Console log with library version
- Add discounts to show in plan template
- Upgrating version of @aplazo/web-ui to 1.0.5
- Upgrating version of @aplazopartner-core to 1.0.2

### Changed

- fixed add-card component mobile defects

## [1.0.6-RELEASE] - 2021-09-22

### Added

- Console log with library version

## [1.0.5-RELEASE] - 2021-09-22

### Added

- The payment button is disabled when clicking. Re-enabled on payment failure.
- Upgrating version of @aplazo/partner-core to 1.0.1
- Upgrating version of @aplazo/web-ui to 1.0.3

## [1.0.0-RELEASE] - 2021-08-18

### Added

- [AE-367](https://aplazo.atlassian.net/browse/AE-367)
  As Aplazo, I want to start adding the new screens from Santiago to our flow.
