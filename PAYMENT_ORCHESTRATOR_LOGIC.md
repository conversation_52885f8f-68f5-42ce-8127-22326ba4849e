# Payment Orchestrator Logic Documentation

## Overview
Este documento explica la lógica del `PaymentOrchestratorComponent` para determinar cuándo navegar al `SelectInstallmentComponent` vs `PlanConfirmationComponent`.

## Flujo de Decisión Principal

### 1. Recepción de Esquemas
El `PaymentOrchestratorComponent` recibe los esquemas de pago del backend a través de `checkoutInfo$`.

### 2. Verificación de Query Parameters
```typescript
const queryInstallments = this.getQueryInstallments();

if (queryInstallments) {
  const matchedScheme = this.getSchemeByInstallments(data.schemes, queryInstallments);
  if (matchedScheme) {
    // Va directo a PlanConfirmation (saltándose SelectInstallment)
    this._pagesFacade.setCurrentPage(Pages.planConfirmation);
    this._schemeFacade.setInfoCheckout(matchedScheme);
    return;
  }
}
```

### 3. Evaluación de Cuotas Dinámicas
Si NO hay query parameters válidos, evalúa si debe mostrar selección de cuotas:

```typescript
if (this.hasDynamicInstallments(data.schemes)) {
  this._pagesFacade.setCurrentPage(Pages.selectPlan); // → SelectInstallmentComponent
} else {
  this._pagesFacade.setCurrentPage(Pages.planConfirmation);
  this._schemeFacade.setInfoCheckout(data.schemes[0]);
}
```

## Método `hasDynamicInstallments()`

```typescript
private hasDynamicInstallments(schemes: IScheme[]): boolean {
  return !schemes.some(scheme => scheme.id === -1) && schemes.length > 1;
}
```

### Condiciones para mostrar SelectInstallmentComponent:
- ✅ **NO hay esquemas con `id === -1`** (esquemas especiales)
- ✅ **Hay MÁS de 1 esquema disponible** (`schemes.length > 1`)

## Mapeo de Páginas

| Enum | Valor | Componente |
|------|-------|------------|
| `Pages.selectPlan` | `'select-plan'` | **SelectInstallmentComponent** |
| `Pages.planConfirmation` | `'checkout'` | **PlanConfirmationComponent** |

## Casos de Uso

### ✅ Casos donde va a SelectInstallmentComponent:

#### 1. Sin query parameters + múltiples esquemas
```
URL: /walmart-cashi-payment
Esquemas: [
  { id: 1, installments: 3 }, 
  { id: 2, installments: 5 }, 
  { id: 3, installments: 8 }
]
Resultado: → SelectInstallmentComponent
```

#### 2. Query parameters inválidos + múltiples esquemas
```
URL: /walmart-cashi-payment?installments=12  // No existe esquema de 12 cuotas
Esquemas: [
  { id: 1, installments: 3 }, 
  { id: 2, installments: 5 }
]
Resultado: → SelectInstallmentComponent (fallback)
```

### ❌ Casos donde NO va a SelectInstallmentComponent:

#### 1. Query parameters válidos
```
URL: /walmart-cashi-payment?installments=8
Esquemas: [
  { id: 1, installments: 3 }, 
  { id: 3, installments: 8 }
]
Resultado: → PlanConfirmationComponent (con esquema de 8 cuotas preseleccionado)
```

#### 2. Solo un esquema disponible
```
Esquemas: [{ id: 1, installments: 3 }]
Resultado: → PlanConfirmationComponent (con único esquema)
```

#### 3. Esquema especial (id === -1)
```
Esquemas: [{ id: -1, installments: 1 }]
Resultado: → PlanConfirmationComponent (esquema especial)
```

## Lógica de Query Parameters

### Método `getQueryInstallments()`
1. **Extrae parámetro `installments`** de la URL
2. **Valida que sea un número válido** (> 0)
3. **Fallback a sessionStorage** para flujo Cashi (`cashi-installments`)
4. **Retorna `null`** si no encuentra valor válido

### Método `getSchemeByInstallments()`
1. **Filtra esquemas** por número de cuotas
2. **Si encuentra uno:** lo retorna
3. **Si encuentra múltiples:** selecciona el de menor ID
4. **Si no encuentra ninguno:** retorna `null`

## Flujo Cashi (Walmart)

### Manejo de SessionStorage
El método `handleCashiFlow()` almacena parámetros cuando:
- URL contiene `walmart-cashi-payment`
- `document.referrer` contiene los query parameters originales

```typescript
sessionStorage.setItem('cashi-installments', installments);
sessionStorage.setItem('cashi-offerId', offerId);
```

## Resumen de la Lógica

**El orchestrator envía al usuario a `SelectInstallmentComponent` cuando:**
- Hay **múltiples opciones de cuotas** disponibles
- **NO hay preselección válida** por query parameters
- **NO hay esquemas especiales** (id === -1)

**En todos los demás casos va directo a `PlanConfirmationComponent`.**
