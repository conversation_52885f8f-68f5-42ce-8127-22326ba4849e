# Partner Payments

## This library is to pay a loan.

## Installation

Partner-payments [Node.js](https://nodejs.org/) v14+ to run.

Install the dependencies and devDependencies and build the library.

```sh
cd angular.partner-payments
npm i
```

To linking the library to your proyect you need follow the next steps.

```sh
ng build --watch
```

Open new tab or terminal

```sh
cd angular.partner-payments/dist/partner-payments
npm link
cd ..
```

You must not be in directory where the files change when the library re-build when any change happens.

After this you need move to your proyect

```sh
npm link @aplazo/partner-payments
```

Enjoy it! :star_struck:
