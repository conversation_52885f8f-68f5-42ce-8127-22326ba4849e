{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"partner-payments": {"projectType": "library", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/partner-payments", "sourceRoot": "projects/partner-payments/src", "prefix": "aplazo-payments", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "projects/partner-payments/tsconfig.lib.json", "project": "projects/partner-payments/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/partner-payments/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/partner-payments/src/test.ts", "tsConfig": "projects/partner-payments/tsconfig.spec.json", "karmaConfig": "projects/partner-payments/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/partner-payments/tsconfig.lib.json", "projects/partner-payments/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}}, "cli": {"analytics": false, "cache": {"enabled": false}}}