pipeline {
  agent {
    node {
      label 'Jenkins-Slave'
    }
  }
  stages {
    stage('SonarQube Analysis') {
      when {
        branch 'integration/develop'
      }
      steps {
        script {
          def scannerHome = tool 'SonarScanner';
          nodejs(nodeJSInstallationName: "node 15.4.0") {
            withSonarQubeEnv() {
              sh "${scannerHome}/bin/sonar-scanner"
            }
          }
          // waitForQualityGate abortPipeline: true
        }
      }
    }
  }
}