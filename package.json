{"name": "partner-payments", "version": "7.2.16", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "build:prod": "ng build partner-payments production", "test": "ng test", "lint": "eslint '**/*.{js,ts}' --fix", "publish:pre": "npm run build && cd dist/partner-payments && npm publish --registry=https://nexus.aplazo.dev/repository/npm-private-aplazo-snapshot/ --tag next && cd ../../", "publish:beta": "npm run build && cd dist/partner-payments && npm publish --registry=https://nexus.aplazo.dev/repository/npm-private-aplazo-snapshot/ --tag beta && cd ../../", "publish:dev": "npm run build && cd dist/partner-payments && npm publish --registry=https://nexus.aplazo.dev/repository/npm-private-aplazo-snapshot/ && cd ../../", "publish:prod": "npm run build && cd dist/partner-payments && npm publish --registry=https://nexus.aplazo.dev/repository/npm-private-aplazo/ && cd ../../"}, "private": true, "dependencies": {"@angular/animations": "~15.2.0", "@angular/cdk": "~15.2.0", "@angular/common": "~15.2.0", "@angular/compiler": "~15.2.0", "@angular/core": "~15.2.0", "@angular/forms": "~15.2.0", "@angular/material": "~15.2.0", "@angular/platform-browser": "~15.2.0", "@angular/platform-browser-dynamic": "~15.2.0", "@angular/router": "~15.2.0", "@aplazo/client-verification": "~5.4.0", "@aplazo/client-virtual-card": "~3.2.6", "@aplazo/partner-analytics": "~3.2.0", "@aplazo/partner-core": "~3.1.0", "@aplazo/partner-styles": "~3.0.1", "@aplazo/web-ui": "~4.1.0", "@aplazo/front-feature-flags": "1.29.4", "@kushki/js": "~1.31.4", "@ngneat/transloco": "^4.1.0", "@ngrx/effects": "~15.4.0", "@ngrx/entity": "~15.4.0", "@ngrx/store": "~15.4.0", "@ngrx/store-devtools": "~15.4.0", "@vgs/collect-js": "~0.6.1", "angular-google-tag-manager": "1.7.0", "date-fns": "^2.22.1", "ngx-mask": "15.1.5", "rxjs": "7.8.1", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~15.2.0", "@angular/cli": "~15.2.0", "@angular/compiler-cli": "~15.2.0", "@types/eslint": "8.4.3", "@types/jasmine": "~3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.26.0", "codelyzer": "^6.0.0", "eslint": "^8.16.0", "eslint-plugin-promise": "^6.0.0", "jasmine-core": "~3.8", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.3.13", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "ng-packagr": "^15.2.2", "ngx-infinite-scroll": "~15.0.0", "prettier": "^2.5.1", "protractor": "~7.0.0", "stylelint": "^13.13.1", "stylelint-config-standard": "^22.0.0", "stylelint-scss": "^3.21.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "tslint-config-prettier": "^1.18.0", "typescript": "~4.9.5"}}