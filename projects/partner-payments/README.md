# PartnerPayments

This library was generated with [Angular CLI](https://github.com/angular/angular-cli) version 10.2.4.

## Code scaffolding

Run `ng generate component component-name --project partner-payments` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module --project partner-payments`.
> Note: Don't forget to add `--project partner-payments` or else it will be added to the default project in your `angular.json` file. 

## Build

Run `ng build partner-payments` to build the project. The build artifacts will be stored in the `dist/` directory.

## Publishing

After building your library with `ng build partner-payments`, go to the dist folder `cd dist/partner-payments` and run `npm publish`.

## Running unit tests

Run `ng test partner-payments` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
