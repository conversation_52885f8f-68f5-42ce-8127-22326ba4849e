import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { WebUiModule } from '@aplazo/web-ui';

import { CheckoutRoutingModule } from './checkout-routing.module';
import { checkoutPaymentFeatureName } from './store/state/checkout.state';
import { checkoutPaymentReducer } from './store/reducers/checkout.reducer';
import { PageEffects } from './store/effects/page.effects';
import { ComponentsModule } from './components/components.module';
import { CheckoutInfoEffects } from './store/effects/checkout-info.effect';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    CheckoutRoutingModule,
    WebUiModule,
    ComponentsModule,
    StoreModule.forFeature(checkoutPaymentFeatureName, checkoutPaymentReducer),
    EffectsModule.forFeature([PageEffects, CheckoutInfoEffects]),
  ],
})
export class CheckoutModule {}
