import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TemplatesModule } from './templates/templates.module';
import { MoleculesModule } from './molecules/molecules.module';
import { WebUiModule } from '@aplazo/web-ui';

@NgModule({
  declarations: [],
  imports: [CommonModule, WebUiModule, TemplatesModule, MoleculesModule],
  exports: [TemplatesModule, MoleculesModule],
})
export class ComponentsModule {}
