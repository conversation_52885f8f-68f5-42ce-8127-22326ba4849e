<div class="aplazo-modal-validation">
  <button mat-dialog-close class="aplazo-modal-validation__close">
    <mat-icon aria-hidden="false" aria-label="close icon">close</mat-icon>
  </button>

  <!-- title -->
  <p class="aplazo-modal-validation__title">{{ data.title }}</p>
  <!-- Subtitle -->
  <p class="aplazo-modal-validation__subtitle">
    {{ data.subtitle }}<br />
    <span>{{ data.dateFormat }}</span>
  </p>
  <div class="aplazo-modal-validation__input">
    <p class="aplazo-modal-validation__input__hint">{{ data.birthdate }}</p>
    <mat-form-field appearance="outline">
      <input
        [formControl]="validationControl"
        [placeholder]="data.placeholder"
        [minlength]="8"
        [maxlength]="10"
        inputmode="decimal"
        type="text"
        matInput
        mask="XX/XX/XXXX"
        placeholder="XXXXXXXX"
        [hiddenInput]="hidden"
        autocomplete="off"
      />
      <mat-icon matSuffix (click)="toggleVisibility()">{{ hidden ? 'visibility_off' : 'visibility' }}</mat-icon>
      <mat-error *ngIf="validationControl.invalid">{{ data.invalidDate }}</mat-error>
    </mat-form-field>
    <p *ngIf="validationControl.valid && !hidden && dateIsValid()">{{ getFormattedDate() }}</p>
  </div>

  <div class="aplazo-modal-validation__button">
    <aplazo-button
      [title]="data.continue"
      [isRounded]="true"
      [isFullWidth]="true"
      [isDisabled]="validationControl.invalid"
      (actionSelected)="submit()"
    ></aplazo-button>
  </div>
</div>
