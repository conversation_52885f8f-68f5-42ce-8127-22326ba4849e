.aplazo-modal-validation {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  &__close {
    background-color: transparent;
    border-radius: 50%;
    border: transparent 0px solid;
    color: #000000;
    cursor: pointer;
    font-size: 9.5px;
    height: 44px;
    width: 44px;
    align-self: end;
    &:focus {
      border-color: transparent;
    }
  }
  &__title {
    font-family: var(--font-bold);
    color: var(--color-black-1);
    font-size: 20px;
    line-height: 28px;
    text-align: center;
    margin-bottom: 1rem;
  }
  &__subtitle {
    color: var(--color-black-1);
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    margin-bottom: 1rem;
    span {
      font-family: var(--font-bold);
      color: var(--color-black-1);
    }
  }
  &__button {
    width: 100%;
    margin-top: 1rem;
  }
  &__input {
    width: 100%;
    margin-top: 1rem;
    &__hint {
      color: var(--color-black-1);
      font-size: 12px;
      text-align: left;
    }
    input {
      font-family: var(--font-bold);
      font-size: 18px;
    }
  }
}

:host ::ng-deep .mat-form-field {
  .mat-form-field-wrapper {
    margin: 0 0 8px !important;
    padding-bottom: 16px;
  }

  .mat-form-field-invalid {
    .mat-form-field-wrapper {
      padding-bottom: 32px !important;
    }
  }

  .mat-form-field-outline {
    &-start {
      border-radius: 50px 0 0 50px !important;
      min-width: 50px !important;
      border-color: rgba(39, 41, 55, 0.12);
    }
    &-end {
      border-radius: 0 50px 50px 0 !important;
      min-width: 50px !important;
      border-color: rgba(39, 41, 55, 0.12);
    }
  }

  .mat-form-field-flex {
    padding: 0 20px !important;
  }

  .mat-form-field-infix {
    border-top: 0 !important;
    padding: 14px 0 !important;
    margin-top: 8px;
  }

  .mat-input-element::placeholder {
    color: rgba(39, 41, 55, 0.35) !important;
  }
}
