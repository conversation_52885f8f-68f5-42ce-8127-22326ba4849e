import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalAccountValidationComponent } from './modal-account-validation.component';

describe('ModalAccountValidationComponent', () => {
  let component: ModalAccountValidationComponent;
  let fixture: ComponentFixture<ModalAccountValidationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalAccountValidationComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalAccountValidationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
