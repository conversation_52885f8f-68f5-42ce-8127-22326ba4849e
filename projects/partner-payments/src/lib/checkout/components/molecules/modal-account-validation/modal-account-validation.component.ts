import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { format } from 'date-fns';
import { es } from 'date-fns/esm/locale';
import { Subject, take, takeUntil } from 'rxjs';
import { UiCoreFacade, PARTNER_CORE_ENVIRONMENT, PartnerCoreEnvironment } from '@aplazo/partner-core';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { ErrorsService } from '../../../services/errors/errors.service';
import { RiskValidationService } from '../../../../payment-methods/services/risk-validation/risk-validation.service';
import { ErrorModalTemplateComponent } from '@aplazo/web-ui';

@Component({
  selector: 'aplazo-payments-modal-account-validation',
  templateUrl: './modal-account-validation.component.html',
  styleUrls: ['./modal-account-validation.component.scss'],
})
export class ModalAccountValidationComponent implements OnInit, OnDestroy {
  validationControl: FormControl;

  public hidden = true;

  private unsubscribe$ = new Subject<boolean>();

  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _fb: FormBuilder,
    private _dialog: MatDialog,
    private _uiFacade: UiCoreFacade,
    private _errorsService: ErrorsService,
    private _riskValidationService: RiskValidationService,
    public _dialogRef: MatDialogRef<ModalAccountValidationComponent>
  ) {
    this.validationControl = this._fb.control(null, [Validators.required, this.dateValidator.bind(this)]);
  }
  ngOnInit(): void {
    this.validationControl.valueChanges.pipe(takeUntil(this.unsubscribe$)).subscribe(value => {
      if (this.validationControl.value.length >= 8) {
        this.validationControl.markAllAsTouched();
      }
    });
  }

  submit() {
    this._uiFacade.setLoading(true);
    const date = this.validationControl.value;
    const [day, month, year] = [date.substring(0, 2), date.substring(2, 4), date.substring(4)];
    this.validationControl.setValue('');

    this._riskValidationService
      .validateChallenge(this.data.loanId, `${year}-${month}-${day}`)
      .pipe(take(1))
      .subscribe({
        next: res => {
          this._uiFacade.setLoading(false);
          this._dialogRef.close({
            continue: true,
            challengeToken: res?.token,
          });
        },
        error: err => {
          // PRECONDITION_FAILED
          this._uiFacade.setLoading(false);
          let errorData = {};
          if (err?.error?.code === 'CHALLENGE_VALIDATION_FAILED') {
            errorData = this.data.ERROR_MODALS.ERROR_4702;
          } else if (err?.error?.code === 'MAX_INTENT_REACHED') {
            errorData = this.data.ERROR_MODALS.ERROR_4701;
          } else {
            errorData = this.data.ERROR_MODALS.DEFAULT;
          }

          this._dialog
            .open(ErrorModalTemplateComponent, {
              disableClose: err.error.code === 'MAX_INTENT_REACHED',
              maxWidth: '400px',
              height: 'auto',
              width: '100%',
              hasBackdrop: true,
              autoFocus: false,
              data: errorData,
            })
            .afterClosed()
            .subscribe(buttonClicked => {
              if (buttonClicked) {
                if (buttonClicked.buttonClicked.action === 'logout') {
                  this._dialog.closeAll();
                  this._errorsService.logOut();
                }
                if (buttonClicked.buttonClicked.action === 'support') {
                  window.open(this._environment.aplazo.whatsapp, '_blank');
                }
              }
              if (err.error.code === 'MAX_INTENT_REACHED') {
                this._dialog.closeAll();
                this._errorsService.logOut();
              }
            });
        },
      });
  }

  public toggleVisibility() {
    this.hidden = !this.hidden;
  }

  private splitDate(dateString: string): [number, number, number] {
    const day = parseInt(dateString.substring(0, 2));
    const month = parseInt(dateString.substring(2, 4));
    const year = parseInt(dateString.substring(4));
    return [day, month, year];
  }

  getFormattedDate(): string {
    const date = this.validationControl.value;
    const [day, month, year] = this.splitDate(date);
    const formattedDate = format(new Date(year, month - 1, day), "dd 'de' MMMM 'de' yyyy", { locale: es });
    return formattedDate;
  }

  public dateIsValid(): boolean {
    if (this.validationControl.invalid) {
      return false;
    }
    const date = this.validationControl.value;
    const [day, month, year] = this.splitDate(date);
    const newDate = new Date(year, month - 1, day);
    return newDate.getFullYear() === year && newDate.getMonth() === month - 1 && newDate.getDate() === day;
  }

  dateValidator(control: FormControl): { [key: string]: boolean } | null {
    const date = control.value;
    if (!date) {
      return { invalid: true };
    }
    const [day, month, year] = this.splitDate(date);
    const newDate = new Date(year, month - 1, day);
    if (newDate.getFullYear() !== year || newDate.getMonth() !== month - 1 || newDate.getDate() !== day) {
      return { invalid: true };
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
