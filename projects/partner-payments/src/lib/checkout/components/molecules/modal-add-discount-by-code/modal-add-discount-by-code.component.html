<div class="add-discount-by-code__wrap">
  <button mat-dialog-close class="add-discount-by-code__close">
    <mat-icon aria-hidden="false" aria-label="close icon">close</mat-icon>
  </button>
  <img class="add-discount-by-code__img" [src]="txtTemplate.image" alt="add-discount-by-code-image" />
  <p class="add-discount-by-code__title">{{ txtTemplate.title }}</p>
  <p class="add-discount-by-code__description">
    {{ txtTemplate.description }}
  </p>
  <p class="add-discount-by-code__outside-top-hint">
    {{ txtTemplate.code }}
  </p>
  <form>
    <mat-form-field class="add-discount-by-code__form_field" appearance="outline">
      <input
        type="text"
        matInput
        autocomplete="off"
        [placeholder]="txtTemplate.inputPlaceholder"
        class="add-discount-by-code__input"
        [formControl]="discountCodeCtrl"
        (input)="setUpperCase()"
        trim
      />
      <mat-error *ngIf="discountCodeCtrl.hasError('pattern')" class="add-discount-by-code__error">{{
        txtTemplate.codePatternMessage || txtTemplate.codePatternMessageDefault
      }}</mat-error>
    </mat-form-field>
    <aplazo-button
      class="add-discount-by-code__button"
      [title]="txtTemplate.buttonTitle"
      [size]="'normal'"
      [variant]="'primary'"
      [isFullWidth]="true"
      [isRounded]="true"
      [isDisabled]="discountCodeCtrl.pristine || discountCodeCtrl.invalid"
      (actionSelected)="addDiscountByCode()"
    ></aplazo-button>
  </form>
</div>
