.add-discount-by-code {
  &__wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 900px;
  }
  &__close {
    background-color: transparent;
    border-radius: 50%;
    border: transparent 0px solid;
    color: #000000;
    cursor: pointer;
    font-size: 9.5px;
    height: 44px;
    width: 44px;
    align-self: end;
    &:focus {
      border-color: transparent;
    }
  }
  &__img {
    max-width: 268px;
    margin-bottom: 1rem;
    margin: auto;
    margin-bottom: 1rem;
  }
  &__title {
    text-align: center;
    margin-bottom: 0.5rem;
    font-family: var(--font-bold);
    font-size: 22px;
    line-height: 28px;
  }
  &__description {
    text-align: center;
    color: #78909c;
    margin-bottom: 1rem;
  }
  &__input_icon {
    font-size: 0.4rem;
    position: relative;
    top: -1em;
    margin-right: 0.6rem;
  }
  &__button {
    width: 100%;
  }
  &__outside-top-hint {
    font-size: 14px;
    align-self: flex-start;
    color: var(--color-black-1);
  }
}
// hacky way of centering text in input inside form-field
::ng-deep {
  .add-discount-by-code__form_field .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix {
    border-bottom: 0.84375em solid transparent;
  }
}

::ng-deep .mat-form-field {
  .mat-form-field-outline {
    &-start {
      border-radius: 50px 0 0 50px !important;
      min-width: 50px !important;
      border-color: rgba(39, 41, 55, 0.12) !important;
    }
    &-end {
      border-radius: 0 50px 50px 0 !important;
      min-width: 50px !important;
      border-color: rgba(39, 41, 55, 0.12) !important;
    }
  }
}

::ng-deep.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.65em !important;
}
