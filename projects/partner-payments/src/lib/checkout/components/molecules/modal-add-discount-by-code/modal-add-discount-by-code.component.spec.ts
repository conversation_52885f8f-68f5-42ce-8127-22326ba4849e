import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatLegacyDialogRef as MatDialogRef, MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';

import { ModalAddDiscountByCodeComponent } from './modal-add-discount-by-code.component';

import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { AtomsModule, VendorsModule } from '@aplazo/web-ui';
import { iPlanTemplateMock } from '../../../mocks/plan-template.mock';

describe('ModalAddDiscountByCodeComponent', () => {
  let component: ModalAddDiscountByCodeComponent;
  let fixture: ComponentFixture<ModalAddDiscountByCodeComponent>;
  const template = iPlanTemplateMock.customerDiscounts.addDiscountByCode;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, VendorsModule, AtomsModule, NoopAnimationsModule],
      declarations: [ModalAddDiscountByCodeComponent],
      providers: [
        {
          provide: MatDialogRef,
          useValue: {
            close: (dialogResult: any) => console.info(dialogResult),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: template,
        },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalAddDiscountByCodeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render title from template values', () => {
    const titleElement: HTMLHeadingElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__title')
    ).nativeElement;
    expect(titleElement.innerText).toBe(template.title);
  });

  it('should render description from template values', () => {
    const descriptionElement: HTMLParagraphElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__description')
    ).nativeElement;
    expect(descriptionElement.innerText).toBe(template.description);
  });

  it('should render input placeholder from template values', () => {
    const inputElement: HTMLInputElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__input')
    ).nativeElement;
    expect(inputElement.placeholder || inputElement.getAttribute('data-placeholder')).toBe(template.inputPlaceholder);
  });

  it('should render button text from template values', () => {
    const buttonElement: HTMLButtonElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__button button')
    ).nativeElement;
    expect(buttonElement.innerText).toBe(template.buttonTitle);
  });

  it('should mark input as invalid and disable button', () => {
    const buttonElement: HTMLButtonElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__button button')
    ).nativeElement;

    const inputElement: HTMLInputElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__input')
    ).nativeElement;
    expect(inputElement.required).toBeTruthy();

    inputElement.value = 'test.invalid';
    inputElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();
    fixture.whenStable().then(() => {
      expect(inputElement.value).toContain('test.invalid');
      expect(inputElement.attributes).toContain['ng-dirty'];
      expect(inputElement.attributes).toContain['ng-invalid'];
      expect(buttonElement.disabled).toBeTruthy();
    });
  });

  it('should mark input as valid and enabled button', () => {
    const buttonElement: HTMLButtonElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__button button')
    ).nativeElement;

    const inputElement: HTMLInputElement = fixture.debugElement.query(
      By.css('.add-discount-by-code__input')
    ).nativeElement;
    expect(inputElement.required).toBeTruthy();

    inputElement.value = 'test';
    inputElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();
    fixture.whenStable().then(() => {
      expect(inputElement.value).toContain('test');
      expect(inputElement.attributes).toContain['ng-valid'];
      expect(buttonElement.disabled).toBeFalsy();
    });
  });
});
