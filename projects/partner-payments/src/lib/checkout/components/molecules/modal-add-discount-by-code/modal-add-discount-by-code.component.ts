import { Component, Inject } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, Validators } from '@angular/forms';
import { MatLegacyDialogRef as MatDialogRef, MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { IAddDiscountByCodeTemplate } from '../../../interfaces/add-discount-by-code-template';

@Component({
  selector: 'aplazo-modal-add-discount-by-code',
  templateUrl: './modal-add-discount-by-code.component.html',
  styleUrls: ['./modal-add-discount-by-code.component.scss'],
})
export class ModalAddDiscountByCodeComponent {
  private readonly defaultPattern = /^[a-zA-Z0-9]+$/;
  txtTemplate: IAddDiscountByCodeTemplate;
  discountCodeCtrl: UntypedFormControl;
  constructor(
    public dialogRef: MatDialogRef<ModalAddDiscountByCodeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: UntypedFormBuilder
  ) {
    this.txtTemplate = data;
    this.discountCodeCtrl = this.fb.control(null, [
      Validators.required,
      Validators.pattern(this.txtTemplate?.codePattern || this.defaultPattern),
    ]);
  }

  setUpperCase(){
    this.discountCodeCtrl.setValue( this.discountCodeCtrl.value.toUpperCase())
  }

  addDiscountByCode(): void {
    if (this.discountCodeCtrl.valid) {
      this.dialogRef.close(this.discountCodeCtrl.value);
      this.discountCodeCtrl.reset();
    }
  }
}