import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalAplazoPointsComponent } from './modal-aplazo-points.component';

describe('ModalAplazoPointsComponent', () => {
  let component: ModalAplazoPointsComponent;
  let fixture: ComponentFixture<ModalAplazoPointsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalAplazoPointsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalAplazoPointsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
