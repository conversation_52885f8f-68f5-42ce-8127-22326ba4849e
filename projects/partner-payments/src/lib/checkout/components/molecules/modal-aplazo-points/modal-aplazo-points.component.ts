import { Component, Inject, OnInit } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';

@Component({
  selector: 'aplazo-payments-modal-aplazo-points',
  templateUrl: './modal-aplazo-points.component.html',
  styleUrls: ['./modal-aplazo-points.component.scss'],
})
export class ModalAplazoPointsComponent {
  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {}
}
