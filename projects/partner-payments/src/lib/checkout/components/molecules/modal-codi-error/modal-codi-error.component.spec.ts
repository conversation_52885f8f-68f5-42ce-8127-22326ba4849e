import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalCodiErrorComponent } from './modal-codi-error.component';

describe('ModalCodiErrorComponent', () => {
  let component: ModalCodiErrorComponent;
  let fixture: ComponentFixture<ModalCodiErrorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ModalCodiErrorComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ModalCodiErrorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
