import { Component, Inject } from '@angular/core';
import { WebUiModule } from '@aplazo/web-ui';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';

@Component({
  selector: 'aplazo-payments-modal-codi-error',
  templateUrl: './modal-codi-error.component.html',
  styleUrls: ['./modal-codi-error.component.scss'],
  standalone: true,
  imports: [WebUiModule],
})
export class ModalCodiErrorComponent {
  constructor(@Inject(MAT_DIALOG_DATA) public data: any, public dialogRef: MatDialogRef<ModalCodiErrorComponent>) {}
}
