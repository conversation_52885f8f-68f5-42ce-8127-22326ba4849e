<div class="aplazo-modal-cvv">
  <button mat-dialog-close class="aplazo-modal-cvv__close">
    <mat-icon aria-hidden="false" aria-label="close icon">close</mat-icon>
  </button>

  <!-- title -->
  <p class="aplazo-modal-cvv__title">{{ data.title }}</p>
  <!-- card -->
  <div class="aplazo-modal-cvv__card">
    <!-- Change image card based in brand -->
    <img [src]="cardsWhite[data.card?.brand?.toLowerCase()] ?? cardsColor['none']" />
    <span>••••&nbsp;</span>
    {{ data.card.card_number }}
  </div>

  <!-- input cvv -->
  <div class="aplazo-modal-cvv__input">
    <p class="aplazo-modal-cvv__input__hint">
      {{ data.cvv.hint }}
    </p>
    <mat-form-field appearance="outline">
      <input
        (input)="onInput($event)"
        [formControl]="cvvControl"
        [placeholder]="data?.cvv?.placeholder"
        [minlength]="3"
        [maxlength]="4"
        type="password"
        matInput
        autocomplete="off"
      />
      <mat-error *ngIf="cvvControl.hasError('required') && (cvvControl.dirty || cvvControl.touched)">
        {{ 'ERRORS.INPUTS.required' | transloco }}
      </mat-error>

      <mat-error
        *ngIf="cvvControl.errors && !cvvControl.hasError('required') && (cvvControl.dirty || cvvControl.touched)"
      >
        {{ 'ERRORS.INPUTS.invalid' | transloco }}
      </mat-error>
    </mat-form-field>
  </div>

  <!-- leyend cvv -->
  <div class="aplazo-modal-cvv__alert" *ngIf="data?.showLeyend">
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/icons/icon_info_blue_2.png" />
    <p>{{ data.leyend }}</p>
  </div>

  <div class="aplazo-modal-cvv__button">
    <aplazo-button
      [title]="data.buttonText"
      [isRounded]="true"
      [isFullWidth]="true"
      [isDisabled]="cvvControl.invalid"
      (actionSelected)="submit()"
    ></aplazo-button>
  </div>
</div>
