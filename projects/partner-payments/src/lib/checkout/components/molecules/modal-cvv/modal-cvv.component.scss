.aplazo-modal-cvv {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  &__close {
    background-color: transparent;
    border-radius: 50%;
    border: transparent 0px solid;
    color: #000000;
    cursor: pointer;
    font-size: 9.5px;
    height: 44px;
    width: 44px;
    align-self: end;
    &:focus {
      border-color: transparent;
    }
  }
  &__title {
    font-family: var(--font-bold);
    color: var(--color-black-1);
    font-size: 20px;
    line-height: 28px;
    text-align: center;
    margin-bottom: 1rem;
  }
  &__card {
    display: flex;
    flex-direction: row;
    border: 1px solid rgba(39, 41, 55, 0.12);
    border-radius: 2rem;
    width: 100%;
    padding: 15px;
    img {
      max-height: 28px;
      width: auto;
    }
    span {
      margin-left: 18px;
    }
  }
  &__alert {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    background: #f0faff;
    border-radius: 8px;
    text-align: center;
    color: var(--color-black-1);
    border: 2px #0075ff solid;
    img {
      max-height: 20px;
      width: auto;
      height: 100%;
    }
    p {
      font-family: var(--font-bold);
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      padding: 0 15px;
    }
  }
  &__input {
    width: 100%;
    margin-top: 1rem;
    &__hint {
      color: var(--color-black-1);
      font-size: 12px;
      text-align: left;
    }
  }
  &__button {
    width: 100%;
    margin-top: 1rem;
  }
}

::ng-deep .mat-form-field {
  .mat-form-field-outline {
    &-start {
      border-radius: 50px 0 0 50px !important;
      min-width: 50px !important;
      border-color: rgba(39, 41, 55, 0.12) !important;
    }
    &-end {
      border-radius: 0 50px 50px 0 !important;
      min-width: 50px !important;
      border-color: rgba(39, 41, 55, 0.12) !important;
    }
  }
}

::ng-deep.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.65em !important;
}
