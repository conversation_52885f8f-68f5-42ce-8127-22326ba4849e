import { Component, Inject } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, Validators } from '@angular/forms';
import {
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialogRef as MatDialogRef,
} from '@angular/material/legacy-dialog';
import { UiCoreFacade } from '@aplazo/partner-core';
import { PaymentFacade } from '../../../../payment-methods/store/facades/payment.facade';
import { cardsColor, cardsWhite } from '@aplazo/web-ui';

@Component({
  selector: 'aplazo-payments-modal-cvv',
  templateUrl: './modal-cvv.component.html',
  styleUrls: ['./modal-cvv.component.scss'],
})
export class ModalCvvComponent {
  cvvControl: UntypedFormControl;

  public cardsWhite: typeof cardsWhite = cardsWhite;
  public cardsColor: typeof cardsColor = cardsColor;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _fb: UntypedFormBuilder,
    private _uiFacade: UiCoreFacade,
    private _paymentFacade: PaymentFacade,
    public _dialogRef: MatDialogRef<ModalCvvComponent>
  ) {
    this.cvvControl = this._fb.control(null, [Validators.required, Validators.pattern('^[0-9]{3,4}$')]);
  }

  submit() {
    this._uiFacade.setLoading(true);
    this._dialogRef.close();
    const cvv = this.cvvControl.value;
    this._paymentFacade.sendPayment(
      cvv,
      this.data.loanId,
      this.data.schemeId,
      this.data.useAplazoPoints,
      false,
      this.data.isPayNow,
      this.data.challengeToken
    );
  }

  onInput(event: any) {
    let inputValue = event.target.value;
    const validInput = inputValue.replace(/[^0-9]/g, '');
    if (validInput.length <= 4) {
      this.cvvControl.setValue(validInput);
    }
  }
}
