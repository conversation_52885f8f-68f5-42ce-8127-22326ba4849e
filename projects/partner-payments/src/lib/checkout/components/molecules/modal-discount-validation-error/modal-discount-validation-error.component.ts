import { Component, Inject } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { IDiscountValidationErrorTemplate } from '../../../interfaces/discount-validation-error-template';

@Component({
  selector: 'aplazo-modal-discount-validation-error',
  templateUrl: 'modal-discount-validation-error.component.html',
  styleUrls: ['modal-discount-validation-error.component.scss'],
})
export class ModalDiscountValidationErrorComponent {
  txtTemplate: IDiscountValidationErrorTemplate;
  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {
    this.txtTemplate = data as IDiscountValidationErrorTemplate;
  }
}
