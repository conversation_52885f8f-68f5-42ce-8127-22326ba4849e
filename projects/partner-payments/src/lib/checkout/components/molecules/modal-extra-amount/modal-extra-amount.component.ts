import { Component, Inject } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';

@Component({
  selector: 'aplazo-payments-modal-extra-amount',
  templateUrl: './modal-extra-amount.component.html',
  styleUrls: ['./modal-extra-amount.component.scss']
})
export class ModalExtraAmountComponent {

  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {}

}
