import { Component, Inject } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';

@Component({
  selector: 'aplazo-payments-modal-info-first-installment',
  templateUrl: './modal-info-commisions.component.html',
  styleUrls: ['./modal-info-commisions.component.scss'],
})
export class ModalInfoCommisionsComponent {
  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {}
}
