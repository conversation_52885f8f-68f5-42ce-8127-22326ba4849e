import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalExtraAmountComponent } from './modal-extra-amount/modal-extra-amount.component';
import { WebUiModule } from '@aplazo/web-ui';
import { ModalCommissionComponent } from './modal-commission/modal-commission.component';
import { ModalDiscountComponent } from './modal-discount/modal-discount.component';
import { ModalAddDiscountByCodeComponent } from './modal-add-discount-by-code/modal-add-discount-by-code.component';
import { ModalDiscountValidationErrorComponent } from './modal-discount-validation-error/modal-discount-validation-error.component';
import { PaymentBalanceComponent } from './payment-balance/payment-balance.component';
import { TrimDirective } from '../../directives/trim/trim.directive';
import { ModalAplazoPointsComponent } from './modal-aplazo-points/modal-aplazo-points.component';
import { ModalCvvComponent } from './modal-cvv/modal-cvv.component';
import { ModalAccountValidationComponent } from './modal-account-validation/modal-account-validation.component';
import { NgxMaskDirective } from 'ngx-mask';
import { PaymentCalendarStepperComponent } from './payment-calendar-stepper/payment-calendar-stepper.component';
import { ModalInfoCommisionsComponent } from './modal-info-commisions/modal-info-commisions.component';

@NgModule({
  declarations: [
    ModalExtraAmountComponent,
    ModalCommissionComponent,
    ModalDiscountComponent,
    ModalAddDiscountByCodeComponent,
    ModalDiscountValidationErrorComponent,
    PaymentBalanceComponent,
    TrimDirective,
    ModalAplazoPointsComponent,
    ModalCvvComponent,
    ModalAccountValidationComponent,
    PaymentCalendarStepperComponent,
    ModalInfoCommisionsComponent,
  ],
  exports: [
    ModalExtraAmountComponent,
    ModalCommissionComponent,
    ModalDiscountComponent,
    ModalAddDiscountByCodeComponent,
    ModalDiscountValidationErrorComponent,
    PaymentBalanceComponent,
    PaymentCalendarStepperComponent,
    ModalInfoCommisionsComponent,
  ],
  imports: [CommonModule, WebUiModule, NgxMaskDirective],
})
export class MoleculesModule {}
