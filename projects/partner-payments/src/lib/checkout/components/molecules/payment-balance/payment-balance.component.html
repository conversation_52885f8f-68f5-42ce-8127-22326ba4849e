<aplazo-card-content
  [textTemplate]="{
    title: textTemplate?.title,
    total: textTemplate.total
  }"
  [showSubtitle]="showSubtitle"
  [useFrameBox]="false"
>
  <div content class="aplazo_accordion">
    <mat-accordion class="no-frame">
      <mat-expansion-panel class="no-frame" expanded>
        <mat-expansion-panel-header class="aplazo_accordion__header no-frame no-frame__avoid-height">
          <mat-panel-title class="aplazo_accordion__title"> Ver detalles de compra </mat-panel-title>
        </mat-expansion-panel-header>

        <!-- Cart amount -->
        <p class="aplazo-payment-balance__content aplazo-payment-balance__content-mt">
          <span class="aplazo-payment-balance__subtotal"> Carrito </span>
          <span class="aplazo-payment-balance__subtotal">
            {{ subtotal | currency }}
          </span>
        </p>
        <!-- Discount code -->
        <p
          class="aplazo-payment-balance__content"
          *ngIf="showAddCoupon"
          [class.aplazo-payment-balance__coupons-disabled]="disableCoupons"
        >
          <span class="aplazo-payment-balance"> Código de descuento </span>
          <span
            class="aplazo-payment-balance__add-coupon"
            [class.aplazo-payment-balance__coupons-disabled]="disableCoupons"
            (click)="addCouponClicked()"
          >
            Agregar
          </span>
        </p>

        <!-- Aplazo Points -->
        <p
          class="aplazo-payment-balance__content aplazo-payment-balance__content-cursor"
          *ngIf="showAplazoPointsDiscount"
          (click)="openAplazoPointsModal()"
        >
          <span class="aplazo-payment-balance__commission"> Aplazo puntos </span>
          <img
            src="https://aplazo-assets.s3.us-west-2.amazonaws.com/assets/info.png"
            class="aplazo-payment-balance__content-img"
          />
          <span class="aplazo-payment-balance__commission">{{ aplazoPointsUsed * -1 | currency }}</span>
        </p>

        <!-- Discounts -->
        <p
          *ngFor="let item of discounts; let last = last; let i = index"
          [ngClass]="[
            item.showDescription && item.description ? 'aplazo-payment-balance__content-cursor' : '',
            last ? 'aplazo-payment-balance__content-last' : 'aplazo-payment-balance__content',
            item.isFromDiscountCode ? 'aplazo-payment-balance__content-removable' : ''
          ]"
          (click)="openDiscount(item)"
        >
          <span
            class="aplazo-payment-balance__discount-description"
            [class.aplazo-payment-balance__content-removable-label]="item.isFromDiscountCode"
            [class.aplazo-payment-balance__discount-description-green]="item.isFromDiscountCode"
          >
            {{ item.text }}
            <img
              *ngIf="item.showDescription && item.description"
              src="https://aplazo-assets.s3.us-west-2.amazonaws.com/assets/info.png"
              class="aplazo-payment-balance__content-img"
            />
            <span
              *ngIf="item.isFromDiscountCode"
              (click)="onDiscountRemoved(i)"
              class="aplazo-payment-balance__remove_discount_link"
              >{{ textTemplate.removeDiscount }}</span
            >
          </span>
          <span class="aplazo-payment-balance__discount-amount">{{ item.amount | currency }}</span>
        </p>
        <!-- Subtotal -->

        <p class="aplazo-payment-balance__content aplazo-payment-balance__content-mt">
          <span class="aplazo-payment-balance__subtotal"> {{ textTemplate.subtotal }} </span>
          <span class="aplazo-payment-balance__subtotal">
            {{ purchaseAmount | currency }}
          </span>
        </p>

        <!-- Commision -->
        <p
          class="aplazo-payment-balance__content aplazo-payment-balance__content-cursor"
          (click)="openCommissionPercentage()"
        >
          <span class="aplazo-payment-balance__commission">
            {{ textTemplate.commission }}
          </span>
          <img
            src="https://aplazo-assets.s3.us-west-2.amazonaws.com/assets/info.png"
            class="aplazo-payment-balance__content-img"
          />
          <span class="aplazo-payment-balance__commission">{{ commission | currency }}</span>
        </p>
        <!-- IVA -->
        <p class="aplazo-payment-balance__content aplazo-payment-balance__content-cursor" (click)="openModalIVA()">
          <span class="aplazo-payment-balance__commission">
            {{ textTemplate.iva }}
          </span>
          <img
            src="https://aplazo-assets.s3.us-west-2.amazonaws.com/assets/info.png"
            class="aplazo-payment-balance__content-img"
          />
          <span class="aplazo-payment-balance__commission">{{ ivaAmount ?? 0 | currency }}</span>
        </p>

        <!-- Total -->
        <p class="aplazo-payment-balance__content">
          <span class="aplazo-payment-balance__subtotal"> Total de tu compra </span>
          <span class="aplazo-payment-balance__subtotal">
            {{ total | currency }}
          </span>
        </p>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</aplazo-card-content>
