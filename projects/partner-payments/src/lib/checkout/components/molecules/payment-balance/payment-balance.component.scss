@import '@aplazo/partner-styles/src/scss/_functions';
@import '@aplazo/partner-styles/src/scss/_typography';

.aplazo-payment-balance {
  &__content {
    display: flex;
    align-items: center;
    margin: 5px 0;
    &-mt {
      margin-top: 0.85rem;
    }
    &-last {
      display: flex;
      justify-content: space-between;
      padding-bottom: 0;
    }
    &-img {
      width: 16px;
      height: 16px;
      margin-left: 7px;
    }
    &-cursor {
      cursor: pointer;
    }
    &-removable {
      border: 1px solid var(--color-green-1);
      margin: 0px 0px 10px 0px;
      padding: 2px 4px;
      border-radius: 5px;
      background-color: #f1fffa;
      &-label {
        flex-wrap: wrap;
      }
      &__discount {
        color: var(--color-green-default);
      }
    }
  }
  &__add-coupon {
    color: #272937;
    font-size: 14px;
    text-decoration: underline;
    cursor: pointer;
    font-family: var(--font-semibold);
    margin-left: auto;
  }
  &__coupons-disabled {
    cursor: not-allowed;
  }
  &__subtotal {
    color: var(--color-black);
    font-family: var(--font-bold);
    &:last-child {
      margin-left: auto;
    }
  }

  &__commission {
    font-size: 14px;
    display: flex;
    align-items: center;
    &:last-child {
      width: auto;
      margin-left: auto;
    }
    img {
      border: 1px black solid;
    }
  }
  &__discount {
    color: var(--color-black);
    @include body-small();
    font-family: var(--font-semibold);
    display: flex;
    &-description {
      text-align: start;
      hyphens: auto;
      word-break: break-word;
      padding-right: 10px;
      &-green {
        color: var(--color-green-1);
      }
    }
    &-amount {
      white-space: nowrap;
      margin-left: auto;
    }
  }

  &__remove_discount_link {
    display: inline;
    padding: 0rem 0rem 0rem 0.5rem;
    font-family: var(--font-semibold);
    text-decoration: none;
    cursor: pointer;
    color: var(--color-black);
  }
}

.aplazo_accordion {
  border: 1px solid rgba(39, 41, 55, 0.12);
  border-radius: 30px;
  padding: 15px;
  &__header:hover {
    background: none !important;
  }
  &__title {
    color: var(--color-black);
    font-family: var(--font-bold);
  }
}

:host ::ng-deep {
  .mat-expansion-panel-body {
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  .mat-expansion-panel .mat-expansion-panel-header.cdk-keyboard-focused:not([aria-disabled='true']),
  .mat-expansion-panel .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled='true']),
  .mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled='true']) {
    background: rgba(0, 0, 0, 0.04);
  }
  .mat-expansion-indicator {
    margin-right: 10px;
  }
}

.no-frame {
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
  &__avoid-height {
    height: auto;
  }
}
