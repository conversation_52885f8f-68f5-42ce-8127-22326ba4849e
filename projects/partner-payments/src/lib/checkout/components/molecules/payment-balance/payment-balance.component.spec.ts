import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AtomsModule, DirectivesModule, VendorsModule } from '@aplazo/web-ui';

import { PaymentBalanceComponent } from './payment-balance.component';

xdescribe('PaymentBalanceComponent', () => {
  let component: PaymentBalanceComponent;
  let fixture: ComponentFixture<PaymentBalanceComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, VendorsModule, AtomsModule, DirectivesModule],
      declarations: [PaymentBalanceComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentBalanceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
