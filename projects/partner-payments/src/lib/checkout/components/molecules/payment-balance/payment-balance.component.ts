import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { IDiscount } from '@aplazo/web-ui';
import { IPaymentBalance } from '../../../interfaces/payment-balance.interface';
import { ModalCommissionComponent } from '../modal-commission/modal-commission.component';
import { ModalDiscountComponent } from '../modal-discount/modal-discount.component';
import { ModalAplazoPointsComponent } from '../modal-aplazo-points/modal-aplazo-points.component';
import { ModalInfoCommisionsComponent } from '../modal-info-commisions/modal-info-commisions.component';

@Component({
  selector: 'aplazo-payment-balance',
  templateUrl: './payment-balance.component.html',
  styleUrls: ['./payment-balance.component.scss'],
})
export class PaymentBalanceComponent implements OnInit {
  @Input() textTemplate: IPaymentBalance;
  @Input() total: number;
  @Input() subtotal: number;
  @Input() commission: number;
  @Input() ivaAmount: number;
  @Input() discounts: IDiscount[] = [];
  @Input() commissionPercentage: number;
  @Input() purchaseAmount: number;
  @Input() contactSupportLink: string;
  @Input() extraAmountQuantity: number;
  @Input() showSubtitle: boolean;
  @Input() showAddCoupon: boolean;
  @Input() disableCoupons: boolean;
  @Input() showAplazoPointsDiscount: boolean = false;
  @Input() aplazoPointsUsed: number = 0;
  @Output() discountRemoved = new EventEmitter<number>();
  @Output() addCoupon = new EventEmitter<void>();

  constructor(public dialog: MatDialog) {
    this.total = 0;
    this.subtotal = 0;
    this.commission = 0;
    this.extraAmountQuantity = 0;
  }

  ngOnInit(): void {
    this.commissionPercentage = this.commissionPercentage * 100;
  }

  openDiscount(item: IDiscount): void {
    if (item.showDescription && item.description) {
      this.dialog.open(ModalDiscountComponent, {
        autoFocus: false,
        maxWidth: '375px',
        maxHeight: '100%',
        width: '100%',
        data: {
          description: item.description || '',
        },
      });
    }
  }

  openCommissionPercentage(): void {
    const message =
      this.textTemplate.modalCommission.message_first +
      ` <span class="text-black text-black--bold">${this.commissionPercentage.toFixed(2)}%</span> ` +
      this.textTemplate.modalCommission.message_last;
    this.dialog.open(ModalCommissionComponent, {
      autoFocus: false,
      maxHeight: '100%',
      maxWidth: '400px',
      data: {
        title: this.textTemplate.modalCommission.title,
        message: message,
        image: this.textTemplate.modalCommission?.imageModalCommission ?? null,
      },
    });
  }

  openModalIVA(): void {
    this.dialog.open(ModalInfoCommisionsComponent, {
      autoFocus: false,
      maxHeight: '100%',
      maxWidth: '400px',
      data: this.textTemplate.modalIVA,
    });
  }

  openAplazoPointsModal(): void {
    this.dialog.open(ModalAplazoPointsComponent, {
      autoFocus: false,
      maxHeight: '100%',
      maxWidth: '375px',
      data: {
        image: 'https://aplazoassets.s3.us-west-2.amazonaws.com/messages-images/aplazo-puntos.svg',
        title: '¿Cómo funciona los aplazo puntos?',
        description: 'Obtén estos beneficios de diversas maneras y utilízalos para ahorrar dinero en tus compras',
      },
    });
  }

  onDiscountRemoved(idx: number): void {
    if (this.discounts[idx].isFromDiscountCode) {
      this.discountRemoved.emit(idx);
    }
  }

  addCouponClicked() {
    if (this.disableCoupons) {
      return;
    }
    this.addCoupon.emit();
  }
}
