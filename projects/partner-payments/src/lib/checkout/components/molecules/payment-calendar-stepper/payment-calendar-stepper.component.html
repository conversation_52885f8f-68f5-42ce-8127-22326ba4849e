<div class="aplazo-stepper__container">
  <div
    *ngFor="let step of stepper; first as firstStep"
    class="aplazo-stepper__step"
    [ngClass]="{
      'aplazo-stepper__step__completed': step.status === 'completed',
      'aplazo-stepper__step__active': step.status === 'active',
      'aplazo-stepper__step__empty': step.status === 'empty'
    }"
  >
    <div class="aplazo-stepper__v-stepper">
      <div class="aplazo-stepper__circle"></div>
      <div class="aplazo-stepper__line" [class.aplazo-stepper__line__blue]="step.status === 'active'"></div>
    </div>

    <div class="aplazo-stepper__content">
      <p class="aplazo-stepper__title">
        <span *ngFor="let title of step.title; first as firstTitle">
          {{ title }}
          <img
            *ngIf="firstStep && firstTitle && showModalInfoFirstInstallment"
            src="https://aplazo-assets.s3.us-west-2.amazonaws.com/assets/info.png"
            (click)="clickModalFirstInstallment.emit(true)"
          />
        </span>
      </p>
      <ng-container *ngIf="!isEmptyObject(step.details)">
        <p class="aplazo-stepper__subtitle" (click)="clickModal.emit(true)">
          <span>
            {{ step.details.titleExtraAmount }}
            <img
              src="https://aplazo-assets.s3.us-west-2.amazonaws.com/assets/info.png"
              class="aplazo-stepper__img"
              [alt]="step.details.titleExtraAmount"
            />
          </span>
          <span>{{ step.details.extraAmount }}</span>
        </p>
        <p class="aplazo-stepper__subtitle">
          <span>{{ step.details.titleFirstPay }}</span>
          <span>{{ step.details.firstPay }}</span>
        </p>
      </ng-container>
    </div>
  </div>
</div>
