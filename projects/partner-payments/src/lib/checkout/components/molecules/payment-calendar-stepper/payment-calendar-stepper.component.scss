@import '@aplazo/partner-styles/src/scss/typography';

.aplazo-stepper {
  &__container {
    width: 100%;
  }
  &__step {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    padding-bottom: 12px;
    &:last-child {
      .aplazo-stepper__line {
        border-left: 1px solid var(--color-white);
        z-index: -1; /* behind the circle to completely hide */
      }
    }
  }
  &__circle {
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-3);
    border-radius: 100%;
    width: 16px;
    height: 16px;
    display: inline-block;
  }
  &__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
        margin-left: 8px;
        cursor: pointer;
      }
    }
  }
  &__subtitle {
    display: flex;
    justify-content: space-between;
    span {
      font-size: 12px;
    }
  }
  &__img {
    width: 10px;
    height: 10px;
    margin-left: 7px;
    cursor: pointer;
  }
  &__line {
    top: 18px;
    left: 7px;
    height: 100%;
    position: absolute;
    border-left: 1px solid var(--color-gray-3);
    &__blue {
      border-left: 1px solid #00e6f5;
    }
  }
  &__step__empty {
    .aplazo-stepper__circle {
      visibility: hidden;
    }
    .aplazo-stepper__line {
      top: 0;
      height: 190%;
    }
  }
  &__step__active {
    .aplazo-stepper__circle {
      visibility: visible;
      border-color: #00e6f5;
      background-color: #00e6f5;
    }

    .aplazo-stepper__content {
      color: var(--color-black);
    }

    .aplazo-stepper__title {
      font-size: 16px;
      font-family: var(--font-bold);
    }
  }
  &__step__completed {
    .aplazo-stepper__circle {
      visibility: visible;
      background-color: var(--color-blue-1);
      border-color: var(--color-blue-1);
    }
    .aplazo-stepper__line {
      border-left: 1px solid var(--color-blue-1);
    }
  }
  &__content {
    margin-left: 20px;
    display: block;
    width: 100%;
    @include body-small();
  }
  &__v-stepper {
    position: relative;
  }
}
