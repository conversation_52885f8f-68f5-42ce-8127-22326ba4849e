import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IStep } from '@aplazo/web-ui';

@Component({
  selector: 'aplazo-payment-calendar-stepper',
  templateUrl: './payment-calendar-stepper.component.html',
  styleUrls: ['./payment-calendar-stepper.component.scss'],
})
export class PaymentCalendarStepperComponent {
  @Input() stepper: IStep[];
  @Input() showModalInfoFirstInstallment: boolean = false;
  @Output() clickModal: EventEmitter<boolean> = new EventEmitter<boolean>(false);
  @Output() clickModalFirstInstallment: EventEmitter<boolean> = new EventEmitter<boolean>(false);

  isEmptyObject(obj): boolean {
    return obj && Object.keys(obj).length === 0;
  }
}
