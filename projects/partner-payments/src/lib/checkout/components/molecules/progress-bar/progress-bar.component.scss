@import '@aplazo/partner-styles/src/scss/_functions';

.aplazo-progress-bar {
  display: flex;
  background-color: var(--color-blue-alert);
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  border-radius: 16px;
  padding: 2px 8px 2px 0px;
  width: pixelstorem(85);

  &__circle {
    position: relative;
    width: pixelstorem(25);
    height: pixelstorem(25);
    display: flex;
    justify-content: center;
    align-items: center;

    div {
      position: absolute;
    }

    svg {
      position: relative;
      width: 100%;
      height: 100%;
      max-width: pixelstorem(25);
      transform: rotate(270deg);

      circle {
        width: 100%;
        height: 100%;
        fill: transparent;
        stroke-width: 2;
        stroke: var(--color-blue-1);
        transform: translate(5px, 5px);

        &:nth-child(2) {
          stroke: var(--color-white);
          stroke-dasharray: 50;
          stroke-dashoffset: 50;
        }
      }
    }
  }

  &__time {
    font-size: pixelstorem(14);
    color: var(--color-black);
    font-family: var(--font-bold);
    display: flex;

    span {
      width: pixelstorem(19);
    }
  }
}
