import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, Output, EventEmitter, AfterViewInit } from '@angular/core';

@Component({
  selector: 'aplazo-progress-bar',
  templateUrl: './progress-bar.component.html',
  styleUrls: ['./progress-bar.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class ProgressBarComponent implements OnInit, AfterViewInit {
  public countdownMinutes: string;
  public countdownSeconds: string;

  private targetTime: number;
  private timerInterval: any;
  private totalSeconds: number;

  @Input() targetMinutes: number;
  @Input() targetSeconds: number;

  @Output() timerEnd: EventEmitter<boolean> = new EventEmitter<boolean>();

  ngOnInit(): void {
    this.targetTime = new Date().getTime() + this.targetMinutes * 60 * 1000 + this.targetSeconds * 1000;
    this.totalSeconds = this.targetMinutes * 60 + this.targetSeconds;
  }

  ngAfterViewInit(): void {
    this.initTimer();
  }

  private initTimer(): void {
    const progress = document.getElementById('progress')
    let totalSeconds = this.totalSeconds

    this.timerInterval = setInterval(() => {
      const now = new Date().getTime();
      const difference = this.targetTime - now;
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      totalSeconds = totalSeconds - 1

      progress.style.strokeDashoffset = `${(50 / this.totalSeconds) * totalSeconds}`;

      if (difference <= 0) {
        clearInterval(this.timerInterval);
        this.timerEnd.emit(true);
      } else {
        this.countdownMinutes = `${this.formatTime(minutes)}`
        this.countdownSeconds = `${this.formatTime(seconds)}`
      }
    }, 1000);

  }

  private formatTime(num: number): string {
    // TODO review new docs https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/padStart
    return num < 10 ? `0${num}` : `${num}`;
  }
}
