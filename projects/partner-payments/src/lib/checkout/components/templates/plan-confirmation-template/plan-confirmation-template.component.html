<aplazo-header [showContent]="false"></aplazo-header>
<ng-content [select]="[contentFlowStepper]"></ng-content>
<p class="aplazo-plan-template__title-header">{{ txtTemplate.header.title }}</p>
<div
  class="aplazo-plan-template__extraAmount"
  *ngIf="extraAmountQuantity > 0 && txtTemplate.paymentBalance.extraAmount"
>
  <img
    class="aplazo-plan-template__extraAmount__image"
    [src]="txtTemplate.paymentBalance.extraAmount.image"
    alt="aplazo"
  />
  <div class="aplazo-plan-template__extraAmount__texts">
    <p class="aplazo-plan-template__extraAmount__title">{{ txtTemplate.paymentBalance.extraAmount.title }}</p>
    <p>
      {{ txtTemplate.paymentBalance.extraAmount.subtitle_first }}
      {{ creditLimit | currency }}{{ txtTemplate.paymentBalance.extraAmount.subtitle_second
      }}{{ extraAmountQuantity ?? 0 | currency }}{{ txtTemplate.paymentBalance.extraAmount.subtitle_third }}
    </p>
  </div>
</div>
<section class="aplazo-plan-template__section">
  <aplazo-payment-balance
    [textTemplate]="{
      total: txtTemplate.paymentBalance.total,
      subtotal: txtTemplate.paymentBalance.subtotal,
      tax: txtTemplate.paymentBalance.tax,
      discount: txtTemplate.paymentBalance.discount,
      commission: txtTemplate.paymentBalance.commission,
      purchase: txtTemplate.paymentBalance.purchase,
      modalCommission: txtTemplate.paymentBalance.modalCommission,
      removeDiscount: txtTemplate.paymentBalance.removeDiscount,
      iva: txtTemplate.paymentBalance.iva,
      modalIVA: txtTemplate.paymentBalance.modalIVA,
    }"
    [total]="total"
    [subtotal]="subtotal"
    [discounts]="discounts"
    [commission]="commission"
    [commissionPercentage]="feeCustomer"
    [purchaseAmount]="totalDiscountAmount"
    [contactSupportLink]="contactSupportLink"
    [extraAmountQuantity]="extraAmountQuantity"
    [showSubtitle]="showSubtitle"
    [showAddCoupon]="showCustomerDiscounts"
    [disableCoupons]="areAttemptsExceeded || !allowMoreCodes"
    [aplazoPointsUsed]="aplazoPointsUsed"
    [showAplazoPointsDiscount]="showAplazoPointsDiscount"
    [ivaAmount]="ivaAmount"
    (discountRemoved)="onDiscountRemoved($event)"
    (addCoupon)="addCoupon()"
  ></aplazo-payment-balance>
</section>

<section class="aplazo-plan-template__section">
  <aplazo-card-content
    [textTemplate]="{
      title: customerType === 'BNPL' ? txtTemplate.paymentPlan.title : null,
      subtitle: txtTemplate.paymentPlan.subtitle,
      total: txtTemplate.paymentPlan.total
    }"
    [showSubtitle]="showSubtitle"
    (clickActionEvent)="changePlanEvent()"
    [useFrameBox]="false"
  >
    <aplazo-payment-calendar-stepper
      content
      [stepper]="stepper"
      [showModalInfoFirstInstallment]="showModalInfoFirstInstallment"
      (clickModal)="openModal()"
      (clickModalFirstInstallment)="openModalFirstInstallmentInfo()"
    ></aplazo-payment-calendar-stepper>
  </aplazo-card-content>
</section>
<section class="aplazo-plan-template__section aplazo-plan-template__footer">
  <ng-content select="[content]" class="content"></ng-content>
</section>
