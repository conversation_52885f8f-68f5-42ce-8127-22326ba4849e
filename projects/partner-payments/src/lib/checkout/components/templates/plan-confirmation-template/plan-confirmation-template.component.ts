import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { IDiscount, IStep } from '@aplazo/web-ui';
import { NEVER, Observable, Subject, mergeMap, of, takeUntil } from 'rxjs';
import { ModalExtraAmountComponent } from '../../molecules/modal-extra-amount/modal-extra-amount.component';
import { ModalDiscountValidationErrorComponent } from '../../molecules/modal-discount-validation-error/modal-discount-validation-error.component';
import { ModalAddDiscountByCodeComponent } from '../../molecules/modal-add-discount-by-code/modal-add-discount-by-code.component';
import { ModalInfoCommisionsComponent } from '../../molecules/modal-info-commisions/modal-info-commisions.component';
import {
  DiscountValidationError,
  discountValidationErrors,
  getDiscountValidationError,
} from '../../../interfaces/discount-validation-error-template';
import { ErrorCodes } from '../../../enums/pages.enum';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'aplazo-payments-plan-confirmation-template',
  templateUrl: './plan-confirmation-template.component.html',
  styleUrls: ['./plan-confirmation-template.component.scss'],
})
export class PlanConfirmationTemplateComponent implements OnDestroy {
  @Input() enableAddCoupon: boolean;
  @Input() txtTemplate: any;

  @Input() total: number;

  @Input() subtotal: number;

  @Input() discounts: IDiscount[] = [];

  @Input() commission: number;

  @Input() btnDisabled: boolean;

  @Input() stepper: IStep[];

  @Input() totalDiscountAmount: number;

  @Input() feeCustomer: number;

  @Input() ivaAmount: number;

  @Input() creditLimit: number;

  @Input() extraAmountQuantity: number;

  @Input() contactSupportLink: string;

  @Input() showSubtitle: boolean;

  @Input() customerDiscountsEnabled = false;

  @Input() validateDiscountCode: (voucherCode: string) => Observable<string | null> = () => of(null);

  @Input() aplazoPointsUsed: number = 0;
  @Input() showAplazoPointsDiscount: boolean = false;

  @Output() discountAdded: EventEmitter<void> = new EventEmitter<void>();

  @Output() discountRemoved: EventEmitter<IDiscount> = new EventEmitter<IDiscount>();

  @Output() submitForm: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() changePlan: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input() customerType: 'BNPL' | 'PAY_NOW';

  @Input() showModalInfoFirstInstallment: boolean = false;

  private unsubscribe$ = new Subject<boolean>();
  private _attemptErrorCounter = 0;
  private readonly maxAttemptsWithError = 3;
  public allowMoreCodes = true;

  constructor(public dialog: MatDialog, private currencyPipe: CurrencyPipe) {
    this.btnDisabled = false;
  }

  public onSubmit(): void {
    this.submitForm.emit(true);
  }

  public openModal(): void {
    const creditLimit = this.currencyPipe.transform(Math.round(this.creditLimit * 100) / 100);
    const extraAmount = this.currencyPipe.transform(this.extraAmountQuantity);
    this.dialog.open(ModalExtraAmountComponent, {
      autoFocus: false,
      maxHeight: '100%',
      maxWidth: '400px',
      data: {
        title: this.txtTemplate.paymentBalance.modalExtraPayment.title,
        message: `${this.txtTemplate.paymentBalance.modalExtraPayment.message_1}
        <span class="text-black text-black--bold">${creditLimit}</span>
        ${this.txtTemplate.paymentBalance.modalExtraPayment.message_2}
        <span class="text-black text-black--bold">${extraAmount}</span>
        ${this.txtTemplate.paymentBalance.modalExtraPayment.message_3}`,
        tools: this.txtTemplate.paymentBalance.modalExtraPayment.tools,
        link: this.contactSupportLink,
      },
    });
  }
  public openModalFirstInstallmentInfo(): void {
    const data = this.txtTemplate.paymentBalance.modalFirstInstallmentFee;
    this.dialog.open(ModalInfoCommisionsComponent, {
      autoFocus: false,
      maxHeight: '100%',
      maxWidth: '400px',
      data: data,
    });
  }

  public changePlanEvent(): void {
    this.changePlan.emit(true);
  }

  onDiscountAdded(): void {
    this.discountAdded.emit();
  }

  onDiscountRemoved(idx: number): void {
    const discountRemoved = this.discounts[idx];
    this.discountRemoved.emit(discountRemoved);
  }

  get showCustomerDiscounts(): boolean {
    return this.customerDiscountsEnabled;
  }

  // Logic for coupons now is here!

  addCoupon(retry: boolean = false): void {
    this.dialog
      .open(ModalAddDiscountByCodeComponent, {
        autoFocus: 'input.add-discount-by-code__input',
        maxWidth: '375px',
        maxHeight: '100%',
        width: '100%',
        data: retry
          ? this.txtTemplate.customerDiscounts.retryAddDiscountByCode
          : this.txtTemplate.customerDiscounts.addDiscountByCode,
      })
      .afterClosed()
      .pipe(
        mergeMap((discountCode: string) => (discountCode ? this.validateDiscountCode(discountCode) : NEVER)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe({
        next: discount => {
          if (!discount) {
            this.openValidationErrorDialog('internal');
          } else {
            this._attemptErrorCounter = 0;
            this.discountAdded.emit();
          }
        },
        error: (err: Error) => {
          this._attemptErrorCounter++;
          const validationError = getDiscountValidationError(err.message);
          this.openValidationErrorDialog(this.areAttemptsExceeded ? 'maxAttempts' : validationError);
          this.allowMoreCodes =
            this.getErrorCode(err.message) !== ErrorCodes.INVALID_DISCOUNT_CODE_EXCEDED_THE_MAXIMUM_ALLOWED;
        },
      });
  }

  getErrorCode(message: string): string {
    const splitInit = message.indexOf('[') + 1;
    return message.slice(splitInit, splitInit + 4);
  }

  openValidationErrorDialog(validationError: DiscountValidationError): void {
    if (validationError === discountValidationErrors[6]) {
      this.addCoupon(true);
    } else {
      this.dialog.open(ModalDiscountValidationErrorComponent, {
        autoFocus: false,
        maxWidth: '375px',
        maxHeight: '100%',
        width: '100%',
        data: this.txtTemplate.customerDiscounts.errorTemplates[validationError],
      });
    }
  }

  get areAttemptsExceeded(): boolean {
    return this._attemptErrorCounter >= this.maxAttemptsWithError;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}