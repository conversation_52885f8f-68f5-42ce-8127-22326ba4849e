<aplazo-header [title]="txtTemplate.header.title"></aplazo-header>
<div
  class="aplazo-payments-select-installments-template__next-quincena"
  *ngIf="isPayNextQuincenaActive && installmentsSelected !== 1"
>
  <img [src]="txtTemplate.frameNextQuincena.image" alt="aplazo" />
  <p>{{ txtTemplate.frameNextQuincena.text }}</p>
</div>
<form [formGroup]="formGroup" (ngSubmit)="onSubmit()" class="aplazo__form" autocomplete="off">
  <section class="aplazo-payments-select-installments-template__installments">
    <mat-radio-group formControlName="schemeSelected">
      <mat-radio-button
        *ngFor="let scheme of schemes"
        [value]="scheme"
        class="aplazo-payments-select-installments-template__installment"
        (change)="schemeSelectedChange($event)"
      >
        <div class="aplazo-payments-select-installments-template__installment-content">
          <p class="text-black text-black--bold">
            {{
              (scheme.installments === 1
                ? 'PAYMENTS_MODULE.SELECT_INSTALLMENTS.installments_options.installment'
                : 'PAYMENTS_MODULE.SELECT_INSTALLMENTS.installments_options.installments'
              ) | transloco : { number: scheme.installments }
            }}
          </p>
          <p class="aplazo-payments-select-installments-template__installment-details text-gray-4">
            {{
              (scheme.installments === 1
                ? 'PAYMENTS_MODULE.SELECT_INSTALLMENTS.installments_options.noFeeCustomer'
                : 'PAYMENTS_MODULE.SELECT_INSTALLMENTS.installments_options.feeCustomer'
              ) | transloco : { fee: scheme.interest_amount | currency }
            }}
            <span class="text-black">
              |
              {{
                (scheme.installments === 1
                  ? 'PAYMENTS_MODULE.SELECT_INSTALLMENTS.installments_options.feeAmount'
                  : 'PAYMENTS_MODULE.SELECT_INSTALLMENTS.installments_options.feesAmount'
                )
                  | transloco
                    : {
                        number: scheme.installments,
                        amount: scheme?.fee_amount !== null
                          ? (scheme?.fee_amount | currency)
                          : (scheme?.aplazo_points_amounts?.fee_amount | currency)
                      }
              }}
            </span>
          </p>
        </div>
      </mat-radio-button>
    </mat-radio-group>
  </section>

  <aplazo-button
    [title]="'Continuar'"
    [size]="'normal'"
    [isDisabled]="formGroup.invalid"
    [isRounded]="true"
    [isFullWidth]="true"
  ></aplazo-button>
</form>
