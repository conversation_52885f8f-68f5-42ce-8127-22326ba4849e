@import '@aplazo/partner-styles/src/scss/_functions';

.aplazo-payments-select-installments-template {
  &__installments {
    padding-bottom: 1.5rem;
  }

  &__installment {
    padding: 10px 20px;
    border-radius: 3rem;
    border: 1px solid var(--color-gray-2);
    color: var(--color-black);
    cursor: pointer;
    font-size: 1rem;
    min-height: pixelstorem(60);
    width: 100%;
    display: flex;
    align-items: center;
    text-align: left;
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    &-content {
      padding-left: 0.75rem;
    }

    &-details {
      font-size: 0.75rem;
    }
  }

  &__next-quincena {
    display: flex;
    background-color: #f0faff;
    border: 2px solid #0075ff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 16px;
    img {
      max-height: 20px;
      width: auto;
      height: 100%;
      margin-top: 5px;
    }
    p {
      text-align: left;
      font-family: var(--font-bold);
      color: var(--color-black);
      margin-left: 1rem;
    }
  }
}
::ng-deep {
  .aplazo-header__description {
    display: none; // description is not used and break the design
  }
  .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--color-black);
  }
  .mat-radio-button.mat-accent .mat-radio-inner-circle,
  .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),
  .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-persistent-ripple,
  .mat-radio-button.mat-accent:active .mat-radio-persistent-ripple {
    background-color: var(--color-black) !important;
  }
}
