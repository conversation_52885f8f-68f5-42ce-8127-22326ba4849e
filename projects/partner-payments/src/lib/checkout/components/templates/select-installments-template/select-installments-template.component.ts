import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { IScheme } from '../../../interfaces/checkout-info.interface';

@Component({
  selector: 'aplazo-payments-select-installments-template',
  templateUrl: './select-installments-template.component.html',
  styleUrls: ['./select-installments-template.component.scss'],
})
export class SelectInstallmentsTemplateComponent implements OnInit {
  @Input() formGroup: UntypedFormGroup;

  @Input() schemes: IScheme[];

  @Input() txtTemplate: any;

  @Input() isPayNextQuincenaActive: boolean;

  @Output() submitForm = new EventEmitter<boolean>();

  installmentsSelected: number;

  ngOnInit(): void {
    try {
      const scheme: IScheme = this.formGroup.get('schemeSelected').value;
      this.installmentsSelected = scheme.installments;
    } catch (error) {
      this.installmentsSelected = null;
    }
  }

  schemeSelectedChange(v): void {
    const scheme: IScheme = v.value;
    this.installmentsSelected = scheme.installments;
  }

  public onSubmit(): void {
    this.formGroup.markAllAsTouched();
    this.submitForm.emit(true);
  }
}
