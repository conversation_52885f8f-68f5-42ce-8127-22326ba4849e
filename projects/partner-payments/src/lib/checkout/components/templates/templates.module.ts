import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlanConfirmationTemplateComponent } from './plan-confirmation-template/plan-confirmation-template.component';
import { WebUiModule } from '@aplazo/web-ui';
import { SelectInstallmentsTemplateComponent } from './select-installments-template/select-installments-template.component';
import { MoleculesModule } from '../molecules/molecules.module';
import { EarnedAplazoPointsComponent } from './earned-aplazo-points/earned-aplazo-points.component';



@NgModule({
  declarations: [
    PlanConfirmationTemplateComponent,
    SelectInstallmentsTemplateComponent,
    EarnedAplazoPointsComponent
  ],
  exports: [
    PlanConfirmationTemplateComponent,
    SelectInstallmentsTemplateComponent,
    EarnedAplazoPointsComponent
  ],
  imports: [
    CommonModule,
    MoleculesModule,
    WebUiModule
  ]
})
export class TemplatesModule { }
