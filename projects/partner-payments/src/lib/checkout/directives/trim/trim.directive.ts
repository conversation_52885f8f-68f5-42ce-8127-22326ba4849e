import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';

/**
 * This directive removes empty space
 * at the beginning and end of the typed value.
 */
@Directive({
  selector: 'input[trim]',
})
export class TrimDirective {
  constructor(private control: NgControl) {}

  /**
   * Listens for value change on input field.
   *
   * @param value The input value
   */
  @HostListener('input', ['$event.target.value'])
  onInput(value: string) {
    const trimmed = value.trim();

    this.control.control.setValue(trimmed);
  }
}
