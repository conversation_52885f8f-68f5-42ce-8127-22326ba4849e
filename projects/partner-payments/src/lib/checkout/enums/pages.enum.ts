export enum Pages {
  default = '',
  selectPlan = 'select-plan',
  planConfirmation = 'checkout',
  planConfirmationOld = 'plan-confirmation',
  error = 'error',
  thankyou = 'thankyou',
  other = 'other',
}

export enum OtherPages {
  virtualCard = 'virtual-card/viewcard',
  walmartCashi = 'walmart-cashi/process',
}

export enum ThankyouPages {
  FIRST_INSTALLMENT = '',
  FIRST_INSTALLMENT_NEXT_FORNIGHT = 'pay-next-fornight',
}

export enum ErrorPages {
  SERVER = 'server',
  BANK = 'bank',
  CARD_NO_FUNDS_FIRST_TIME = 'card-no-funds',
  CARD_NO_FUNDS_SECOND_TIME = 'card-no-funds-second-time',
  KYC = 'verification',
  LATE_PAYMENTS = 'late-payments',
  LOAN_ON_HOLD = 'loan-on-hold',
  LOAN_REJECTED_NRT = 'loan-rejected-nrt',
  LOAN_REJECTED_HARD_RULE = 'loan-rejected-hard-rule',
  LOAN_REJECTED_KOUNT_OFFLINE = 'loan-rejected-kount-offline',
  LOAN_REJECTED_NRT_OFFLINE = 'loan-rejected-nrt-offline',
  LOAN_REJECTED_BOTH = 'loan-rejected-both',
  LOAN_REJECTED_KOUNT_APPROVE_NRT = 'loan-rejected-kount-approve-nrt',
  LOAN_REJECTED_NRT_APPROVE_KOUNT = 'loan-rejected-nrt-approve-kount',
  LOAN_REJECTED_BOTH_ONLINE = 'loan-rejected-both-online',
  MERCHANT_CONFIGURATION = 'configuration',
  LOAN_CANCELLED = 'loan-cancelled',
  PURCHASE_LIMIT = 'purchase-limit',
  PURCHASE_LIMIT_PER_DAY = 'purchase-limit-per-day',
  INVALID_DISCOUNT_CODE = 'invalid-discount-code',
  DISCOUNT_SERVICE_ERROR = 'discount-service-error',
  DISCOUNT_SERVICE_EXCEPTION = 'discount-service-exception',
  DISCOUNT_REDEMPTION_ERROR = 'discount-redemption-error',
  REJECTED_KOUNT_VC = 'rejected-kount-vc',
  REJECTED_NRT_VC = 'rejected-nrt-vc',
  REJECTED_BOTH_VC = 'rejected-both-vc',
  LOAN_DIFF_TO_REQUEST = 'loan-status-different-to-request',
  KYC_FALSE = 'kyc-false',
  EXTRA_CREDIT_EXCEEDED = 'extra-credit-exceeded',
  NOT_CREDIT_AVAILABLE = 'no-credit-available',
  CAN_NOT_ADD_CARD = 'cannot-add-card',
  CAN_NOT_ADD_CARD_BY_CONNECTION = 'cannot-add-card-connection',
  PAYMENT_ERROR_409 = 'payment-409', // TBD 409 could be more than 1 case
  INVALID_LINK = 'invalid-link',
}

export enum ErrorCodes {
  PURCHASE_LIMIT_PER_DAY = '406',
  BANK = '417',
  SERVER = '500',
  LOAN_REJECTED_NRT = '4000',
  LOAN_REJECTED_HARD_RULE = '4001',
  LOAN_ON_HOLD = '4002',
  LOAN_REJECTED_KOUNT_OFFLINE = '4003',
  LOAN_REJECTED_NRT_OFFLINE = '4004',
  LOAN_REJECTED_BOTH = '4005',
  LOAN_REJECTED_KOUNT_APPROVE_NRT = '4006',
  LOAN_REJECTED_NRT_APPROVE_KOUNT = '4007',
  LOAN_REJECTED_BOTH_ONLINE = '4008',
  REJECTED_KOUNT_VC = '4009',
  REJECTED_NRT_VC = '4010',
  REJECTED_BOTH_VC = '4011',
  BRANCH_CONFIGURATION = '4020',
  LATE_PAYMENTS = '4100',
  CARD_NO_FUNDS_FIRST_TIME = '4101',
  CARD_NO_FUNDS_SECOND_TIME = '4102',
  KYC = 'APC001',
  MERCHANT_CONFIGURATION = '4400',
  LOAN_CANCELLED = 'LOAN_CANCELLED',
  PURCHASE_LIMIT = 'PURCHASE_LIMIT',
  INVALID_DISCOUNT_CODE = '4301',
  DISCOUNT_SERVICE_ERROR = '4302',
  DISCOUNT_SERVICE_EXCEPTION = '4303',
  DISCOUNT_REDEMPTION_ERROR = '4304',
  INVALID_DISCOUNT_CODE_DISABLED = '4305',
  INVALID_DISCOUNT_CODE_EXPIRED = '4306',
  INVALID_DISCOUNT_CODE_NOT_ACTIVE = '4307',
  INVALID_DISCOUNT_CODE_NOT_IN_CACHE = '4308',
  INVALID_DISCOUNT_CODE_COUPON_ALREADY_APPLIED = '4309',
  INVALID_DISCOUNT_CODE_EXCEDED_THE_MAXIMUM_ALLOWED = '4310',
  INVALID_DISCOUNT_CODE_EXCEEDS_LOAN = '4311',
  LOAN_DIFF_TO_REQUEST = '4411',
  KYC_FALSE = '4412',
  EXTRA_CREDIT_EXCEEDED = '4401',
  NOT_CREDIT_AVAILABLE = '4403',
  CAN_NOT_ADD_CARD = '4500',
  CAN_NOT_ADD_CARD_BY_CONNECTION = '4505',
  ERROR_409 = '409', // TBD 409 could be more than 1 case
  INVALID_LINK = '4600',
}

export enum OtherPaymentMethods {
  CODI = 'codi',
  CODI_SUCCESS = 'codi-success',
}
