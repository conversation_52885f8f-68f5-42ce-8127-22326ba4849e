import { PluginIntegrations } from '@aplazo/partner-core';

const translationKey = new Map<string, string>();

translationKey.set('VC', `PAYMENTS_MODULE.PLAN_CONFIRMATION_PAGE.${PluginIntegrations.VIRTUAL_CARD}`);
translationKey.set(
  PluginIntegrations.WALMART_CASHI,
  `PAYMENTS_MODULE.PLAN_CONFIRMATION_PAGE.${PluginIntegrations.WALMART_CASHI}`
);
translationKey.set('STORES', 'PAYMENTS_MODULE.PLAN_CONFIRMATION_PAGE.STORES');

export { translationKey };
