export interface IAddDiscountByCodeTemplate {
  image: string;
  title: string;
  description: string;
  codePattern: string;
  codePatternMessage: string;
  codePatternMessageDefault: string;
  inputPlaceholder: string;
  buttonTitle: string;
  code?: string
}

export const addDiscountByCodeTemplate: IAddDiscountByCodeTemplate = {
  image: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.IMAGE',
  title: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.TITLE',
  description: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.DESCRIPTION',
  codePattern: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.PATTERN',
  codePatternMessage: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.PATTERN_MESSAGE',
  codePatternMessageDefault: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.PATTERN_MESSAGE_DEFAULT',
  buttonTitle: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.BUTTON_TITLE',
  inputPlaceholder: 'CUSTOMER_DISCOUNTS.ADD_DISCOUNT_BY_CODE.INPUT_PLACEHOLDER',
};
