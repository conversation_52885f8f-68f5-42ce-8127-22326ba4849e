export interface ICheckoutInfo {
  aplazo_points?: number | null | undefined;
  card: {
    card_number: string;
    cvv_update: boolean;
    bbva?: boolean;
    brand: string;
  };
  cart_url?: string;
  creation_date: string;
  credit_available: number;
  customer_credit_available: number;
  customer_type?: 'BNPL' | 'PAY_NOW';
  id: number;
  merchant_id: number;
  next_quincena_enabled: boolean;
  promo_code_enabled: boolean;
  purchase_amount: number;
  schemes: IScheme[];
  source_type: string;
  status: string;
  error?: any;
  risk_validation?: any;
}
export interface IScheme {
  aplazo_points_amounts?: {
    aplazo_points_used: number;
    commission: number;
    fee_amount: number;
    first_installment_extra_amount: number | null;
    interest_amount: number;
    subtotal: number;
    total: number;
    total_discount_amount: number;
    total_first_installment: number;
    upfront_rate?: number;
    first_installment: number | null;
    iva_amount_from_commission_amount?: number;
    iva_amount_plus_commission_amount?: number;
    iva_rate?: number;
    iva_rate_plus_commission_rate?: number;
  };
  calendar: string[];
  commission: number;
  customer_discounts_applied: IDiscount[];
  fee_amount: number;
  first_installment_extra_amount: number | null;
  ghost_commission_amount: number | null;
  id: number;
  installments: number;
  interest_amount: number;
  total: number;
  total_discount_amount: number;
  total_first_installment: number;
  upfront_rate?: number;
  first_installment: number | null;
  iva_amount_from_commission_amount?: number;
  iva_amount_plus_commission_amount?: number;
  iva_rate?: number;
  iva_rate_plus_commission_rate?: number;
}

export interface IDiscount {
  amount: number;
  code: string;
  description: string | null;
  fromDiscountCode: boolean;
  name: string;
  requiredInstallments: boolean | null;
}
