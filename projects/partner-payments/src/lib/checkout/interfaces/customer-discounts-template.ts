import { discountValidationErrorTemplate, IDiscountValidationErrorTemplates } from '../interfaces/discount-validation-error-template';
import { addDiscountByCodeTemplate, IAddDiscountByCodeTemplate } from './add-discount-by-code-template';

export interface ICustomerDiscountsTemplate {
  title: string;
  buttonTitle: string;
  addDiscountByCode: IAddDiscountByCodeTemplate;
  retryAddDiscountByCode: IAddDiscountByCodeTemplate;
  errorTemplates: IDiscountValidationErrorTemplates;
}

export const customerDiscountTemplate: ICustomerDiscountsTemplate = {
  title: 'CUSTOMER_DISCOUNTS.TITLE',
  buttonTitle: 'CUSTOMER_DISCOUNTS.BUTTON_TITLE',
  addDiscountByCode: addDiscountByCodeTemplate,
  retryAddDiscountByCode: addDiscountByCodeTemplate,
  errorTemplates: discountValidationErrorTemplate,
};
