import { tuple } from '@aplazo/web-ui';

export interface IDiscountValidationErrorTemplate {
  image: string;
  title: string;
  description: string;
}

export const discountValidationErrors = tuple(
  'internal',
  'invalid',
  'maxAttempts',
  'codeDisabled',
  'codeExpired',
  'codeNotActive',
  'codeNotInCache',
  'tryLater',
  'couponAlreadyApplied',
  'maximumCoupons',
  'exceedsLoan',
);

export type DiscountValidationError = typeof discountValidationErrors[number];

export interface IDiscountValidationErrorTemplates
  extends Record<DiscountValidationError, IDiscountValidationErrorTemplate> {}

export const discountValidationErrorTemplate: IDiscountValidationErrorTemplates = {
  internal: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.INTERNAL.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.INTERNAL.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.INTERNAL.DESCRIPTION',
  },
  invalid: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.INVALID.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.INVALID.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.INVALID.DESCRIPTION',
  },
  maxAttempts: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.MAX_ATTEMPTS.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.MAX_ATTEMPTS.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.MAX_ATTEMPTS.DESCRIPTION',
  },
  codeDisabled: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.DISABLED.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.DISABLED.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.DISABLED.DESCRIPTION',
  },
  codeExpired: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.EXPIRED.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.EXPIRED.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.EXPIRED.DESCRIPTION',
  },
  codeNotActive: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.NOT_ACTIVE.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.NOT_ACTIVE.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.NOT_ACTIVE.DESCRIPTION',
  },
  codeNotInCache: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.NOT_IN_CACHE.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.NOT_IN_CACHE.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.NOT_IN_CACHE.DESCRIPTION',
  },
  tryLater: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.TRY_LATER.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.TRY_LATER.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.TRY_LATER.DESCRIPTION',
  },
  couponAlreadyApplied: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.COUPON_ALREADY_APPLIED.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.COUPON_ALREADY_APPLIED.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.COUPON_ALREADY_APPLIED.DESCRIPTION',
  },
  maximumCoupons: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.MAXIMUM_COUPONS.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.MAXIMUM_COUPONS.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.MAXIMUM_COUPONS.DESCRIPTION',
  },
  exceedsLoan: {
    image: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.EXCEEDS_LOAN.IMAGE',
    title: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.EXCEEDS_LOAN.TITLE',
    description: 'CUSTOMER_DISCOUNTS.VALIDATION_ERROR.EXCEEDS_LOAN.DESCRIPTION',
  },

};

export const discountValidationErrorCodes = tuple('4301','4302','4303','4304', '4305', '4306', '4307', '4308','4309','4310','4311');

export type DiscountValidationErrorCode = typeof discountValidationErrorCodes[number];

const discountValidationErrorErrorCodes: Record<DiscountValidationErrorCode, DiscountValidationError> = {
  '4301': 'invalid',
  '4302': 'tryLater',
  '4303': 'tryLater',
  '4304': 'invalid',
  '4305': 'codeExpired',
  '4306': 'codeExpired',
  '4307': 'codeNotActive',
  '4308': 'codeNotInCache',
  '4309': 'couponAlreadyApplied',
  '4310': 'maximumCoupons',
  '4311': 'exceedsLoan',
};

export function getDiscountValidationError(message: string): DiscountValidationError | undefined {
  let validationError: DiscountValidationError;
  if (message) {
    const errorCode = discountValidationErrorCodes.find(errorCode => message.includes(`[${errorCode}]`));
    if (errorCode) {
      validationError = discountValidationErrorErrorCodes[errorCode];
    }
  }
  return validationError;
}
