import { tuple } from '@aplazo/web-ui';

export interface IBalanceConfig {
  hideBalance: boolean;
  hideBalanceByMerchantId: boolean;
  hideBalanceMerchants: Array<number>;
}

export interface Merchant {
  id: number;
  name: string;
  url: string;
  uri_image: string;
  canBuyInStore: boolean;
  channel: Channel;
}

export interface IMerchantResponse {
  men: Merchant[];
  women: Merchant[];
}

export interface CustomerLoanPaginated {
  content: CustomerLoan[];
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  pageable: {
    offset: 0;
    pageNumber: 0;
    pageSize: 10;
    paged: true;
    sort: {
      unsorted: boolean;
      sorted: boolean;
      empty: boolean;
    };
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
    unpaged: boolean;
  };
  size: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  totalElements: number;
  totalPages: number;
}

export interface CustomerCreditBalance {
  creditAvailable: number;
  creditLimit: number;
}

export interface CustomerLoan {
  amount: number;
  id: number;
  loanName: string;
  merchantName: string;
  payments: number;
  status: string;
  statusSpanish: string;
  installments?: number;
}

export const FILTER_LOANS = [
  'HISTORICAL',
  'OUTSTANDING',
  'LATE',
  'DEFAULTED',
  'PENDING_PAY',
  'ON_HOLD',
  'HISTORICAL_VC0',
  'HISTORICAL_VCA',
  'CANCELLED',
];

export const FILTER_IN_PROGRESS = ['OUTSTANDING', 'LATE', 'DEFAULTED'];
export const FILTER_COMPLETED = ['HISTORICAL', 'HISTORICAL_VCA', 'HISTORICAL_VC0'];

export const URL_MERCHANT_TO_CUSTOMER =
  'https://aplazoassets.s3.us-west-2.amazonaws.com/data/merchants/merchants_customer_success.json';

export const channels = tuple('ON', 'OFF', 'VC');

export type Channel = (typeof channels)[number];

export const ONLINE_ICON = 'https://aplazoassets.s3.us-west-2.amazonaws.com/icons/globe.svg';
export const OFFLINE_ICON = 'https://aplazoassets.s3.us-west-2.amazonaws.com/icons/shopping-bag.svg';
export const VC_ICON = 'https://aplazoassets.s3.us-west-2.amazonaws.com/icons/credit-card.svg';
