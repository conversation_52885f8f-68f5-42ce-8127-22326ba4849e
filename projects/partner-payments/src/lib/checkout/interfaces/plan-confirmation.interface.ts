import { IScheme } from "@aplazo/partner-core";
import { PaymentTypes } from "../../payment-methods/enums/payment-types.enum";
import { PaymentVersion } from "../enums/payment-versions.enum";

export interface IPlanConfirmation {
    scheme: IScheme;
    paymentType: PaymentTypes;
    loanId: number;
    version: PaymentVersion;
    merchantId: number;
    amount: number;
    useAplazoPoints: boolean;
    usePaymentCodi?: boolean;
    folioCodi?: string;
}