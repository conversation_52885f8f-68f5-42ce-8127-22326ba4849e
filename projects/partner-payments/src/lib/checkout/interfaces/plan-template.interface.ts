import { <PERSON><PERSON><PERSON><PERSON>, IPaymentPlan, IListCardsAdded } from '@aplazo/web-ui';
import { ICustomerDiscountsTemplate } from './customer-discounts-template';
import { IPaymentBalance } from './payment-balance.interface';

export interface IPlanTemplate {
  header: IHeader;
  paymentBalance?: IPaymentBalance;
  paymentPlan?: IPaymentPlan;
  cards?: IListCardsAdded;
  btnContinue: string;
  footer?: any;
  customerDiscounts: ICustomerDiscountsTemplate;
}
