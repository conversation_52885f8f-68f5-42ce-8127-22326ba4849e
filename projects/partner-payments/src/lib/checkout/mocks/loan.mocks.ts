import { PluginIntegrations } from "@aplazo/partner-core";

const loanResponse = {
  id: 45252,
  products: [
    {
      title: 'test',
      imageUrl: 'https://aplazoassets.s3-us-west-2.amazonaws.com/aplazo-logo-png-colores.png',
      count: 1,
      price: 1000.0,
    },
  ],
  purchaseAmount: 1000.0,
  interestAmount: 90.0,
  total: 1090.0,
  installments: 5,
  merchantId: 12,
  creationDate: '2022-08-08T03:03:08.705556',
  discountAplazo: 0.0,
  status: 'REQUEST',
  shippingCost: 0.0,
  firstInstallmentExtraAmount: 0.0,
  cartUrl: 'https://posui.aplazo.dev',
  discounts: [],
  feeAmount: 218.0,
  feeCustomer: 0.09,
  totaDiscountAmount: 1000.0,
};

const loanOnline = {
  ...loanResponse,
  integrationType: PluginIntegrations.WOO,
  isOnline: true,
  shopId: null
}

const loanOffline = {
  ...loanResponse,
  integrationType: PluginIntegrations.POSUI,
  isOnline: true,
  shopId: null
}

const loanWithExtraAmountResponse = {
  ...loanOnline,
  interestAmount: 87.43,
  total: 1087.43,
  firstInstallmentExtraAmount: 28.58,
  feeAmount: 211.77,
};


const loanErrorMerchantConfiguration = {
  error: { status: 0, error: 'FI' },
  status: 400
}

const loanErrorNoCreditAvailable = {
  error: { ...loanOnline },
  status: 412
}

const loanErrorNoFound = {
  status: 400,
  error: {}
}

export const loanResponseType = {
  successOffline: loanOffline,
  successOnline: loanOnline,
  successWithExtraAmount: loanWithExtraAmountResponse,
	errorMerchantConfiguration: loanErrorMerchantConfiguration,
  errorNoCrediAvailable: loanErrorNoCreditAvailable,
  errorNoFound: loanErrorNoFound
};
