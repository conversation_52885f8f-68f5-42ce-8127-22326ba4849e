import { emptyPixel } from '@aplazo/web-ui';
import { IPlanTemplate } from '../interfaces/plan-template.interface';

export const iPlanTemplateMock: IPlanTemplate = {
  header: {
    title: 'Confirma tu compra',
    description: 'Para finalizar tu compra realiza el primero de tus 5 pagos.',
  },
  paymentBalance: {
    title: 'TU COMPRA',
    subtotal: 'Subtotal',
    tax: '(IVA incl.)',
    discount: 'Descuento de Aplazo',
    commission: 'Comisión por servicio',
    total: 'Total',
    removeDiscount: 'Eliminar'
  },
  paymentPlan: {
    title: 'TU PLAN DE PAGOS',
    total: 'Total',
  },
  btnContinue: 'Realizar primer pago {{payment}}',
  cards: {
    title: 'MÉTODO DE PAGO',
    // action: 'AGREGAR',
    add: 'AGREGAR',
    replace: 'REEMPLAZAR',
    btnContinue: 'Comprar',
  },
  customerDiscounts: {
    title: 'CÓDIGO DE DESCUENTO',
    buttonTitle: 'AGREGAR',
    addDiscountByCode: {
      image: emptyPixel,
      title: 'Ingresa tu código de descuento',
      description: 'Agrega tu código de descuento en el recuadro de abajo y da click en aplicar código. Verás el descuento aplicado en el resumen de tu compra.',
      codePattern: '^[a-zA-Z0-9]+$',
      codePatternMessage: 'Código debe estar formado por letras y números',
      codePatternMessageDefault: 'Código debe estar formado por letras y números',
      buttonTitle: 'APLICAR CÓDIGO',
      inputPlaceholder: 'Ingresa tu código',
    },
    errorTemplates: {
      internal: {
        image: emptyPixel,
        title: 'Error al validar código.',
        description: 'Ocurrió un error al validar tú código intenta nuevamente.',
      },
      invalid: {
        image: emptyPixel,
        title: 'El código que ingresaste es incorrecto.',
        description: 'Verifica que el código esté bien escrito y que no haya expirado.',
      },
      maxAttempts: {
        image: emptyPixel,
        title: 'Algo ha salido mal.',
        description: 'Haz excedido el número de intentos  para hacer válido tu código..',
      },
      codeDisabled: undefined,
      codeExpired: undefined,
      codeNotActive: undefined,
      codeNotInCache: undefined,
      tryLater: undefined,
      couponAlreadyApplied: undefined,
      maximumCoupons: undefined,
      exceedsLoan: undefined
    },
    retryAddDiscountByCode: undefined
  },
};
