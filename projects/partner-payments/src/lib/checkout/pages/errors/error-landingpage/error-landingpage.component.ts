import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AppStateFacade, UiCoreFacade } from '@aplazo/partner-core';
import { TranslocoService } from '@ngneat/transloco';
import { filter, first, Subject, switchMap, takeUntil } from 'rxjs';
import { ButtonsActions } from '../../../enums/buttons-actions.enum';
import { ErrorCodes } from '../../../enums/pages.enum';
import { ErrorMessageTemplate } from '../../../interfaces/error-message-template.interface';
import { ErrorsService } from '../../../services/errors/errors.service';
import { CheckoutInfoFacade } from '../../../store/facades/checkout-info.facade';

@Component({
  selector: 'aplazo-payments-error-landingpage',
  templateUrl: './error-landingpage.component.html',
})
export class ErrorLandingpageComponent implements OnInit, OnDestroy {
  public txtTemplate: ErrorMessageTemplate;

  public codeMessage: string;

  private _code: string;

  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  constructor(
    private _route: ActivatedRoute,
    private _translocoService: TranslocoService,
    private _uiFacade: UiCoreFacade,
    private _errorsService: ErrorsService,
    private _checkoutInfoFacade: CheckoutInfoFacade,
    private _appStateFacade: AppStateFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._route.data
      .pipe(
        filter(x => !!x),
        first(),
        switchMap(({ code }: { code: string }) => {
          this._code = code ?? ErrorCodes.SERVER;
          return this._checkoutInfoFacade.checkoutInfo$.pipe(filter(checkoutInfo => !!checkoutInfo));
        }),
        switchMap(({ id }) => {
          return this._errorsService.getErrorCode(this._code, id);
        }),
        switchMap(code => {
          this.codeMessage = code;
          return this._translocoService.selectTranslateObject(`ERRORS_BACKEND.${this._code}`, {
            whatsAppLink: {
              code,
            },
          });
        }),
        takeUntil(this._unsubscribe$)
      )
      .subscribe((error: ErrorMessageTemplate) => {
        this.txtTemplate = error;
        this._uiFacade.setLoading(false);
        this._uiFacade.setShowButtonCancel(false);
      });
  }

  public clickActionButtons(btnSelected: number): void {
    this._getButtonAction(this.txtTemplate.actions.buttons[btnSelected].action);
  }

  public ngOnDestroy(): void {
    this._appStateFacade.resetError();
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }

  private _getButtonAction(action: ButtonsActions): void {
    switch (action) {
      case ButtonsActions.contactSupport:
        this._errorsService.openContactSupport(this.txtTemplate.whatsAppLink);
        break;
      case ButtonsActions.goBack:
        this._errorsService.paymentReset();
        break;
      case ButtonsActions.goToDashboard:
        this._errorsService.goToDashboard();
        break;
      case ButtonsActions.goToStore:
        this._errorsService.goToOtherStores();
        break;
      case ButtonsActions.logOut:
        this._errorsService.logOut();
        break;
      case ButtonsActions.returnToStore:
        this._errorsService.returnToStore();
        break;
      default:
        this._errorsService.logOut(); // TBD validate if it's ok
    }
  }
}
