<aplazo-message-template
  *ngIf="txtTemplate"
  [type]="txtTemplate.status"
  [imageUrl]="txtTemplate?.imageUrl"
  [txtTemplate]="txtTemplate"
  [contactSupportLink]="txtTemplate.whatsAppLink"
  [showTools]="true"
  [showContent]="true"
  [codeMessage]="codeMessage"
  (btnSelected)="clickActionButtons()"
>
  <aplazo-payments-payment-methods
    content
    [paymentData]="{
      paymentType,
      loanId: checkoutInfo.id,
      schemeId: schemeSelected.id
    }"
    [checkoutInfo]="checkoutInfo"
    [paymentButtonFixed]="false"
    [btnPaymentText]="txtTemplate.btnPaymentText"
    [schemeSelected]="schemeSelected"
    [paymentMethodsEnabled]="paymentsMethodsEnabled"
    [customerType]="checkoutInfo.customer_type"
    [useAplazoPoints]="useAplazoPoints"
    (cardAddedEvent)="refreshCustomer($event)"
    [showPromoAplazoPoints]="showPromoAplazoPoints"
    [promoCashback]="promoCashback"
  ></aplazo-payments-payment-methods>
</aplazo-message-template>
