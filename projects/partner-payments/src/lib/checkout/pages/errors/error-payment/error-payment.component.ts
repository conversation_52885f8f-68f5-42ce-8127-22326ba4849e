import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AppStateFacade, CustomerFacade, ITypeNotification, UiCoreFacade } from '@aplazo/partner-core';
import { Subject, combineLatest, filter, first, from, switchMap, take, takeUntil, tap, withLatestFrom } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { ErrorsService } from '../../../services/errors/errors.service';
import { PaymentTypes } from '../../../../payment-methods/enums/payment-types.enum';
import { TranslocoService } from '@ngneat/transloco';
import { CheckoutInfoFacade } from '../../../store/facades/checkout-info.facade';
import { SchemeFacade } from '../../../store/facades/scheme.facade';
import { ICheckoutInfo, IScheme } from '../../../interfaces/checkout-info.interface';
import { PlanConfirmationFacade } from '../../../store/facades/plan-confirmation.facade';
import { FeatureFlagsService } from '@aplazo/front-feature-flags';
import { PaymentMethodsEnabledService } from '../../../../payment-methods/services/utils/set-payment-methods.enabled.service';
import { SPLITS } from '../../../../payment-methods/enums/splits.enum';
import { PaymentMethodsEnum } from '../../../../payment-methods/enums/payment-methods.enum';
import { CodiService } from '../../../../payment-methods/services/codi/codi.service';
import { ModalCodiErrorComponent } from '../../../components/molecules/modal-codi-error/modal-codi-error.component';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';

@Component({
  selector: 'aplazo-payments-error-payment',
  templateUrl: './error-payment.component.html',
})
export class ErrorPaymentComponent implements OnInit, OnDestroy {
  public txtTemplate: Record<string, string>;

  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  public paymentType: PaymentTypes;

  public schemeSelected: IScheme;

  public btnPaymentText: string;

  public showOtherPaymentMethods: boolean;

  public codeMessage: string;

  private _code: string;

  public checkoutInfo: ICheckoutInfo;
  public useAplazoPoints: boolean = false;

  public paymentsMethodsEnabled: PaymentMethodsEnum[];

  // CASHBACK for codi
  public showPromoAplazoPoints: boolean = false;
  public promoCashback: string = null;

  constructor(
    private _route: ActivatedRoute,
    private _translocoService: TranslocoService,
    private _uiFacade: UiCoreFacade,
    private _errorsService: ErrorsService,
    private readonly _checkoutInfoFacade: CheckoutInfoFacade,
    private readonly _schemeFacade: SchemeFacade,
    private readonly _appStateFacade: AppStateFacade,
    private readonly _planConfirmationFacade: PlanConfirmationFacade,
    private readonly _customerFacade: CustomerFacade,
    private readonly _featureFlagsService: FeatureFlagsService,
    private _paymentMethodsEnabled: PaymentMethodsEnabledService,
    private _codiService: CodiService,
    private _ngZone: NgZone,
    private _dialog: MatDialog
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._route.data
      .pipe(
        filter(x => !!x),
        first(),
        switchMap(({ code }: { code: string }) => {
          this._code = code;
          return combineLatest([
            this._checkoutInfoFacade.checkoutInfo$,
            this._schemeFacade.getSchemeSelected$,
            this._planConfirmationFacade.useAplazoPoints$,
          ]);
        }),
        switchMap(([checkoutInfo, scheme, useAplazoPoints]) => {
          this.useAplazoPoints = useAplazoPoints;
          this.checkoutInfo = checkoutInfo;
          this.paymentType = checkoutInfo.next_quincena_enabled
            ? PaymentTypes.firstInstallmentNextFornight
            : PaymentTypes.firstInstallment;
          this.schemeSelected = scheme;
          return this._errorsService.getErrorCode(this._code, this.checkoutInfo.id);
        }),
        switchMap(code => {
          this.codeMessage = code;
          return this._translocoService.selectTranslateObject(`ERRORS_BACKEND.${this._code}`, {
            whatsAppLink: {
              code,
            },
          });
        }),
        switchMap((txtTemplate: Record<string, string>) => {
          this.txtTemplate = txtTemplate;
          return [];
        }),
        takeUntil(this._unsubscribe$)
      )
      .subscribe();

    this.setMethodPaymentsAvailable();
    this._subscribePayment();
    this.codiErrorHandler();
    this._subscribeCheckoutInfo();
    this._uiFacade.setShowButtonCancel(false);
  }

  private _subscribePayment(): void {
    this._errorsService.payLoan$.pipe(takeUntil(this._unsubscribe$)).subscribe(() => {
      this._uiFacade.setLoading(false);
    });
  }

  private _subscribeCheckoutInfo(): void {
    this._checkoutInfoFacade.checkoutInfo$
      .pipe(withLatestFrom(this._schemeFacade.getSchemeSelected$), takeUntil(this._unsubscribe$))
      .subscribe(data => {
        this.checkoutInfo = data[0];
        const schemeSelected = data[1];
        this.schemeSelected = this.checkoutInfo.schemes.find(scheme => scheme.id === schemeSelected.id);
        this._schemeFacade.setInfoCheckout(this.schemeSelected);
        this.paymentType = this.checkoutInfo.next_quincena_enabled
          ? PaymentTypes.firstInstallmentNextFornight
          : PaymentTypes.firstInstallment;
        this._uiFacade.setLoading(false);
      });
  }

  setMethodPaymentsAvailable(): void {
    this._customerFacade.me$
      .pipe(
        filter(Boolean),
        take(1),
        switchMap(me => {
          return from(this._featureFlagsService.startLoggedClient(me.id.toString())).pipe(
            switchMap(() => from(this._featureFlagsService.setLoggedAttributes({ id: me.id }))),
            withLatestFrom(this._featureFlagsService.getLoggedFeature$(SPLITS.SPLIT_METHOD_PAYMENTS)),
            tap(() => this.setPromoCashback())
          );
        })
      )
      .subscribe({
        next: ([, ff]) => {
          const paymentMethodsEnabled = Object.entries(ff.config).reduce((acc, [key, value]) => {
            acc[key] = value === 'true';
            return acc;
          }, {} as { [key: string]: boolean });
          this.paymentsMethodsEnabled = this._paymentMethodsEnabled.getPaymentMethodsEnabled(paymentMethodsEnabled);
        },
        error: err => {
          console.error('Error fetching feature flag for payment methods:',err);
          this.paymentsMethodsEnabled = this._paymentMethodsEnabled.getPaymentMethodsEnabled({ card: true });
        },
      });
  }

  private setPromoCashback(): void {
    this._featureFlagsService
      .getLoggedFeature$(SPLITS.SPLIT_CASHBACK)
      .pipe(take(1))
      .subscribe({
        next: featureFlagValue => {
          if (featureFlagValue.value) {
            this.promoCashback = featureFlagValue.value.toString();
            this.showPromoAplazoPoints = true;
          }
        },
        error: err => {
          this.showPromoAplazoPoints = false;
          console.error('Error fetching feature flag:', err);
        },
      });
  }

  private codiErrorHandler(): void {
    this._codiService
      .getCodiError$()
      .pipe(
        takeUntil(this._unsubscribe$),
        withLatestFrom(this._translocoService.selectTranslateObject('OTHER_PAYMENT_METHODS.CODI'))
      )
      .subscribe(([res, txtTemplate]) => {
        if (res.error.code === '4609') {
          this._uiFacade.displayNotification(
            ITypeNotification.error,
            'Se produjo un error inesperado. Inténtelo de nuevo.'
          );
        }
        if (res.error.code === '4601') {
          this.openCodiErrorModal(txtTemplate.ERROR_4601, res.error);
        }
        if (res.error.code === '4603') {
          this.openCodiErrorModal(txtTemplate.ERROR_4603, res.error);
        }
        this._ngZone.run(() => {
          this._uiFacade.setLoading(false);
        });
      });
  }

  private openCodiErrorModal(data: any, error): void {
    this._errorsService
      .getErrorCode(error.code, this.checkoutInfo.id)
      .pipe(take(1))
      .subscribe(code => {
        this._dialog.open(ModalCodiErrorComponent, {
          disableClose: false,
          maxWidth: '400px',
          width: '100%',
          data: {
            ...data,
            errorCode: code,
          },
        });
      });
  }

  public clickActionButtons(): void {
    this._appStateFacade.resetError();
    this._errorsService.paymentReset();
  }

  public refreshCustomer(cardAdded: boolean) {
    if (cardAdded) {
      this._uiFacade.setLoading(true);
      this._checkoutInfoFacade.refreshCheckout$();
    }
  }

  public ngOnDestroy(): void {
    this._appStateFacade.resetError();
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
