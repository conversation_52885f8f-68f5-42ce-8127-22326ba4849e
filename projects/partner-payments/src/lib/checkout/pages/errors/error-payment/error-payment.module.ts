import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ErrorPaymentRoutingModule } from './error-payment-routing.module';
import { ErrorPaymentComponent } from './error-payment.component';
import { WebUiModule } from '@aplazo/web-ui';
import { PaymentMethodsModule } from '../../../../payment-methods';


@NgModule({
  declarations: [
    ErrorPaymentComponent
  ],
  imports: [
    CommonModule,
    WebUiModule,
    PaymentMethodsModule,
    ErrorPaymentRoutingModule
  ]
})
export class ErrorPaymentModule { }
