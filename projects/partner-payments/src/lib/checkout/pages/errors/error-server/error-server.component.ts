import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AppStateFacade, UiCoreFacade } from '@aplazo/partner-core';
import { TranslocoService } from '@ngneat/transloco';
import { filter, first, Subject, switchMap, takeUntil } from 'rxjs';
import { ErrorsService } from '../../../services/errors/errors.service';

@Component({
  selector: 'aplazo-payments-error-server',
  templateUrl: './error-server.component.html',
})
export class ErrorServerComponent implements OnInit {
  public txtTemplate: Record<string, string>;

  public codeMessage: string;

  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  constructor(
    private _route: ActivatedRoute,
    private _translocoService: TranslocoService,
    private _uiFacade: UiCoreFacade,
    private _errorsService: ErrorsService,
    private readonly _appStateFacade: AppStateFacade,
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._route.queryParams
      .pipe(
        filter(x => !!x),
        first(),
        switchMap(({ code }) => {
          return this._errorsService.getErrorCode(code as string);
        }),
        switchMap(code => {
          this.codeMessage = code;
          return this._translocoService.selectTranslateObject('ERRORS_BACKEND.500', {
            whatsAppLink: {
              code,
            },
          });
        }),
        takeUntil(this._unsubscribe$)
      )
      .subscribe((error: Record<string, string>) => {
        this.txtTemplate = error;
        this._uiFacade.setLoading(false);
        this._uiFacade.setShowButtonCancel(false);
      });
  }

  public clickActionButtons(buttonSelected: number): void {
    if (!buttonSelected) {
      this._errorsService.paymentReset();
    } else {
      this._errorsService.openContactSupport(this.txtTemplate.whatsAppLink);
    }
  }

  public ngOnDestroy(): void {
    this._appStateFacade.resetError();
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
