import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ErrorCodes, ErrorPages } from '../../enums/pages.enum';

const routes: Routes = [
  {
    path: ErrorPages.BANK,
    loadChildren: () => import('./error-payment/error-payment.module').then(m => m.ErrorPaymentModule),
    data: {
      code: ErrorCodes.BANK,
    },
  },
  {
    path: ErrorPages.CARD_NO_FUNDS_FIRST_TIME,
    loadChildren: () => import('./error-payment/error-payment.module').then(m => m.ErrorPaymentModule),
    data: {
      code: ErrorCodes.CARD_NO_FUNDS_FIRST_TIME,
    },
  },
  {
    path: ErrorPages.CARD_NO_FUNDS_SECOND_TIME,
    loadChildren: () => import('./error-payment/error-payment.module').then(m => m.ErrorPaymentModule),
    data: {
      code: ErrorCodes.CARD_NO_FUNDS_SECOND_TIME,
    },
  },
  {
    path: ErrorPages.KYC,
    loadChildren: () => import('./error-payment/error-payment.module').then(m => m.ErrorPaymentModule),
    data: {
      code: ErrorCodes.KYC,
    },
  },
  {
    path: ErrorPages.LATE_PAYMENTS,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LATE_PAYMENTS,
    },
  },
  {
    path: ErrorPages.LOAN_ON_HOLD,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_ON_HOLD,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_HARD_RULE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_HARD_RULE,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_KOUNT_OFFLINE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_KOUNT_OFFLINE,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_NRT,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_NRT,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_NRT_OFFLINE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_NRT_OFFLINE,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_BOTH,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_BOTH,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_KOUNT_APPROVE_NRT,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_KOUNT_APPROVE_NRT,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_NRT_APPROVE_KOUNT,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_NRT_APPROVE_KOUNT,
    },
  },
  {
    path: ErrorPages.LOAN_REJECTED_BOTH_ONLINE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_REJECTED_BOTH_ONLINE,
    },
  },
  {
    path: ErrorPages.MERCHANT_CONFIGURATION,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.MERCHANT_CONFIGURATION,
    },
  },
  {
    path: ErrorPages.LOAN_CANCELLED,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_CANCELLED,
    },
  },
  {
    path: ErrorPages.PURCHASE_LIMIT,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.PURCHASE_LIMIT,
    },
  },
  {
    path: ErrorPages.PURCHASE_LIMIT_PER_DAY,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.PURCHASE_LIMIT_PER_DAY,
    },
  },
  {
    path: ErrorPages.INVALID_DISCOUNT_CODE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.INVALID_DISCOUNT_CODE,
    },
  },
  {
    path: ErrorPages.DISCOUNT_SERVICE_ERROR,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.DISCOUNT_SERVICE_ERROR,
    },
  },
  {
    path: ErrorPages.DISCOUNT_SERVICE_EXCEPTION,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.DISCOUNT_SERVICE_EXCEPTION,
    },
  },
  {
    path: ErrorPages.DISCOUNT_REDEMPTION_ERROR,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.DISCOUNT_REDEMPTION_ERROR,
    },
  },
  {
    path: ErrorPages.REJECTED_KOUNT_VC,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.REJECTED_KOUNT_VC,
    },
  },
  {
    path: ErrorPages.REJECTED_NRT_VC,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.REJECTED_NRT_VC,
    },
  },
  {
    path: ErrorPages.REJECTED_BOTH_VC,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.REJECTED_BOTH_VC,
    },
  },
  {
    path: ErrorPages.KYC_FALSE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.KYC_FALSE,
    },
  },
  {
    path: ErrorPages.LOAN_DIFF_TO_REQUEST,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.LOAN_DIFF_TO_REQUEST,
    },
  },
  {
    path: ErrorPages.EXTRA_CREDIT_EXCEEDED,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.EXTRA_CREDIT_EXCEEDED,
    },
  },
  {
    path: ErrorPages.NOT_CREDIT_AVAILABLE,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.NOT_CREDIT_AVAILABLE,
    },
  },
  {
    path: ErrorPages.CAN_NOT_ADD_CARD,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.CAN_NOT_ADD_CARD,
    },
  },
  {
    path: ErrorPages.CAN_NOT_ADD_CARD_BY_CONNECTION,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.CAN_NOT_ADD_CARD_BY_CONNECTION,
    },
  },
  {
    path: ErrorPages.PAYMENT_ERROR_409,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.ERROR_409,
    },
  },
  {
    path: ErrorPages.INVALID_LINK,
    loadChildren: () => import('./error-landingpage/error-landingpage.module').then(m => m.ErrorLandingpageModule),
    data: {
      code: ErrorCodes.INVALID_LINK,
    },
  },
  {
    path: ErrorPages.SERVER,
    loadChildren: () => import('./error-server/error-server.module').then(m => m.ErrorServerModule),
  },
  {
    path: '**',
    redirectTo: ErrorPages.SERVER,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ErrorsRoutingModule { }
