<aplazo-header></aplazo-header>
<p class="aplazo-codi__title-header" [innerHTML]="txtTemplate.header.title"></p>
<img class="aplazo-codi__image" [src]="txtTemplate.PAYMENT.image" alt="aplazo" />
<h5 class="aplazo-codi__title">{{ txtTemplate.PAYMENT.amountCopy }}</h5>
<h4 class="aplazo-codi__amount">{{ paymentAmount | currency }}</h4>
<div class="aplazo-codi__countdown">
  <aplazo-progress-bar
    (timerEnd)="timerEnd()"
    [targetMinutes]="txtTemplate.PAYMENT.timeMinutes"
    [targetSeconds]="txtTemplate.PAYMENT.timeSeconds"
  ></aplazo-progress-bar>
</div>
<div class="aplazo-codi__steps" *ngFor="let step of txtTemplate.PAYMENT.steps; let i = index">
  <span class="aplazo-codi__steps-number">{{ i + 1 }}</span>
  <p [innerHTML]="step.text | htmlSanitizer"></p>
</div>
