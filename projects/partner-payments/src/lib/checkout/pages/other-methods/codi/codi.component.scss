@import '@aplazo/partner-styles/src/scss/_functions';

.aplazo-codi {
  &__image {
    margin-bottom: pixelstorem(30);
  }

  &__title {
    font-size: pixelstorem(20);
    line-height: pixelstorem(28);
    color: var(--color-gray-9);
    font-family: var(--font-regular);
    margin-bottom: pixelstorem(8);
  }

  &__title-header {
    color: var(--color-black);
    text-align: center;
    font-family: var(--font-bold);
    font-size: pixelstorem(24);
    line-height: pixelstorem(32);
    margin-bottom: pixelstorem(11);
  }

  &__amount {
    margin-bottom: pixelstorem(48);
  }

  &__countdown {
    margin: pixelstorem(48) 0;
    display: flex;
    justify-content: center;
  }

  &__steps {
    color: var(--color-black);
    text-align: start;
    display: flex;

    &-number {
      background-color: var(--color-blue-alert);
      height: pixelstorem(27);
      min-width: pixelstorem(27);
      border-radius: 50%;
      font-family: var(--font-bold);
      text-align: center;
      margin-right: pixelstorem(12);
      position: relative;
    }
  }
}
