import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AtomsModule, DirectivesModule, VendorsModule } from '@aplazo/web-ui';

import { CodiComponent } from './codi.component';

xdescribe('CodiComponent', () => {
  let component: CodiComponent;
  let fixture: ComponentFixture<CodiComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, VendorsModule, AtomsModule, DirectivesModule],
      declarations: [CodiComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CodiComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
