import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';
import {
  Subject,
  Subscription,
  catchError,
  combineLatest,
  interval,
  of,
  switchMap,
  take,
  throwError,
  withLatestFrom,
} from 'rxjs';
import { PlanConfirmationFacade } from '../../../store/facades/plan-confirmation.facade';
import { UiCoreFacade } from '@aplazo/partner-core';
import { SchemeFacade } from '../../../store/facades/scheme.facade';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { ModalCodiErrorComponent } from '../../../components/molecules/modal-codi-error/modal-codi-error.component';
import { PagesFacade } from '../../../store/facades/page.facede';
import { Pages } from '../../../enums/pages.enum';
import { CodiService } from '../../../../payment-methods/services/codi/codi.service';
import { HttpErrorResponse } from '@angular/common/http';
import { PaymentCheckoutService } from '../../../services/payment-checkout/payment-checkout.service';
import { CheckoutInfoFacade } from '../../../store/facades/checkout-info.facade';
import { PaymentFacade } from '../../../../payment-methods/store/facades/payment.facade';
import { ErrorsService } from '../../../services/errors/errors.service';

@Component({
  selector: 'aplazo-codi',
  templateUrl: './codi.component.html',
  styleUrls: ['./codi.component.scss'],
})
export class CodiComponent implements OnInit, OnDestroy {
  public showHeader = true;
  public txtTemplate: any;
  public paymentAmount: number;

  private interval$ = interval(10 * 1000);
  private codiStatusSubscription: Subscription;
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  constructor(
    private _translocoService: TranslocoService,
    private _planConfirmationFacade: PlanConfirmationFacade,
    private _uiFacade: UiCoreFacade,
    private readonly _schemeFacade: SchemeFacade,
    private _ngZone: NgZone,
    private _dialog: MatDialog,
    private _pagesFacade: PagesFacade,
    private _codiService: CodiService,
    private _paymentCheckoutService: PaymentCheckoutService,
    private _paymentFacade: PaymentFacade,
    private _checkoutInfoFacade: CheckoutInfoFacade,
    private _errorsService: ErrorsService
  ) {
    this._uiFacade.setLoading(true);
  }

  ngOnInit(): void {
    this._translocoService
      .selectTranslateObject('OTHER_PAYMENT_METHODS')
      .pipe(
        take(1),
        withLatestFrom(this._schemeFacade.getSchemeSelected$, this._planConfirmationFacade.useAplazoPoints$)
      )
      .subscribe(([txtTemplate, schemeSelected, useAplazoPoints]) => {
        this.paymentAmount = useAplazoPoints
          ? schemeSelected.aplazo_points_amounts.total_first_installment
          : schemeSelected.total_first_installment;
        this.txtTemplate = txtTemplate.CODI;
        this.initCodiStatusSubscription();
        this._ngZone.run(() => {
          this._uiFacade.setLoading(false);
        });
      });
  }

  public timerEnd(): void {
    this._errorsService
      .getErrorCode('4602')
      .pipe(take(1))
      .subscribe(code => {
        const data = {
          ...this.txtTemplate.ERROR_4602,
          errorCode: code,
        };
        this.openCodiErrorModal(data);
        this.codiStatusSubscription.unsubscribe();
      });
  }

  private openCodiErrorModal(data: any): void {
    this._dialog
      .open(ModalCodiErrorComponent, {
        disableClose: false,
        maxWidth: '400px',
        width: '100%',
        data,
      })
      .afterClosed()
      .pipe(take(1))
      .subscribe(_ => {
        this._paymentFacade.reset();
        this._pagesFacade.setCurrentPage(Pages.planConfirmation);
      });
  }

  private initCodiStatusSubscription(): void {
    this.codiStatusSubscription = this.interval$
      .pipe(withLatestFrom(this._planConfirmationFacade.folioCodi$))
      .subscribe(([_, folioCodi]) => {
        if (!folioCodi) {
          return;
        }
        this._codiService
          .codiStatus(folioCodi?.toString())
          .pipe(
            take(1),
            catchError((err: HttpErrorResponse) => {
              if (err.error.code && err.error.code === '4603') {
                return this._errorsService.getErrorCode('4603').pipe(
                  take(1),
                  switchMap(code => {
                    const data = {
                      ...this.txtTemplate.ERROR_4603,
                      errorCode: code,
                    };
                    this.openCodiErrorModal(data);
                    return of(null);
                  })
                );
              }
              return throwError(err); // throw other errors
            })
          )
          .subscribe(res => {
            if (res.status === 'PAID') {
              this.codiStatusSubscription.unsubscribe();
              this._uiFacade.setLoading(true);
              combineLatest({
                paymentResponse: this._paymentFacade.payLoan$,
                checkoutInfo: this._checkoutInfoFacade.checkoutInfo$,
                useCodi: this._planConfirmationFacade.usePaymentCodi$,
              }).subscribe(({ paymentResponse, checkoutInfo, useCodi }) => {
                this._paymentCheckoutService._paymentSuccessHandler(
                  paymentResponse.response,
                  checkoutInfo.next_quincena_enabled,
                  checkoutInfo.purchase_amount,
                  checkoutInfo.id,
                  checkoutInfo.merchant_id,
                  useCodi
                );
              });
            }
          });
      });
  }

  public ngOnDestroy(): void {
    this.codiStatusSubscription.unsubscribe();
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
