import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatRadioModule } from '@angular/material/radio';

import { WebUiModule } from '@aplazo/web-ui';

import { CodiRoutingModule } from './codi-routing.module';
import { CodiComponent } from './codi.component';

import { ProgressBarComponent } from '../../../components/molecules/progress-bar/progress-bar.component';

@NgModule({
  declarations: [CodiComponent],
  imports: [CommonModule, CodiRoutingModule, MatRadioModule, WebUiModule, ProgressBarComponent],
})
export class CodiModule {}
