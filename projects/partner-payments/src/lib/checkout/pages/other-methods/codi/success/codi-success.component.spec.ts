import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AtomsModule, DirectivesModule, VendorsModule } from '@aplazo/web-ui';

import { CodiSuccessComponent } from './codi-success.component';

xdescribe('CodiSuccessComponent', () => {
  let component: CodiSuccessComponent;
  let fixture: ComponentFixture<CodiSuccessComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, VendorsModule, AtomsModule, DirectivesModule],
      declarations: [CodiSuccessComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CodiSuccessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
