import { Component, Inject, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  CustomerFacade,
  IMe,
  PARTNER_CORE_ENVIRONMENT,
  PartnerCoreEnvironment,
  UiCoreFacade,
} from '@aplazo/partner-core';
import { TranslocoService } from '@ngneat/transloco';
import { PagesFacade } from '../../../../store/facades/page.facede';
import { combineLatest, take, takeUntil, from, switchMap, Observable, Subject } from 'rxjs';
import { CheckoutInfoFacade } from '../../../../store/facades/checkout-info.facade';
import { FeatureFlagsService } from '@aplazo/front-feature-flags';
import { ICheckoutInfo } from '../../../../interfaces/checkout-info.interface';
import { SPLITS } from '../../../../../payment-methods/enums/splits.enum';

@Component({
  selector: 'aplazo-codi-success',
  templateUrl: './codi-success.component.html',
  styleUrls: ['./codi-success.component.scss'],
})
export class CodiSuccessComponent implements OnInit, OnDestroy {
  public txtTemplate: any;
  private checkoutInfo: ICheckoutInfo;
  private me: IMe;
  public showPromoAplazoPoints: boolean = false;
  public promoCashback: string = null;
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  constructor(
    private _translocoService: TranslocoService,
    private _ngZone: NgZone,
    private _uiFacade: UiCoreFacade,
    public _pagesFacade: PagesFacade,
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _checkoutInfoFacade: CheckoutInfoFacade,
    private _featureFlagService: FeatureFlagsService,
    private _customerFacade: CustomerFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  ngOnInit(): void {
    this._subscribeTranslate();
  }

  private _subscribeTranslate(): void {
    combineLatest({
      checkoutInfo: this._checkoutInfoFacade.checkoutInfo$,
      txtTemplate: this._translocoService.selectTranslateObject('OTHER_PAYMENT_METHODS.CODI'),
      me: this._customerFacade.me$.pipe(take(1)),
    })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe(data => {
        this.checkoutInfo = data.checkoutInfo;
        this.me = data.me;
        this.txtTemplate = data.txtTemplate;
        this.processLoggedFeatureFlag(SPLITS.SPLIT_CASHBACK);
      });
  }

  private processLoggedFeatureFlag(featureFlag: string) {
    this._featureFlagService.refresh();
    from(this._featureFlagService.startLoggedClient(this.me.id.toString()))
      .pipe(
        switchMap(() => this.setLoggedAttributes()),
        switchMap(() => this._featureFlagService.getLoggedFeature$(featureFlag).pipe(take(1)))
      )
      .subscribe(featureFlagValue => {
        if (featureFlagValue.value) {
          this.promoCashback = featureFlagValue.value.toString();
          this.showPromoAplazoPoints = true;
        }
        this._ngZone.run(() => {
          this._uiFacade.setShowButtonCancel(false);
          this._uiFacade.setLoading(false);
        });
      });
  }

  private setLoggedAttributes(): Observable<any> {
    const hourOfDay = +new Date().toLocaleTimeString('es', { timeZone: 'America/Mexico_City' }).split(':')[0];
    return from(
      this._featureFlagService.setLoggedAttributes({
        customerId: String(this.me.id),
        operatingSystem: 'web',
        merchantsId: this.checkoutInfo.merchant_id,
        sourceType: this.checkoutInfo.source_type,
        isNextQuincena: this.checkoutInfo.next_quincena_enabled,
        customerType: this.checkoutInfo.customer_type,
        isPayNow: this.checkoutInfo.customer_type === 'PAY_NOW',
        hourOfDay,
      })
    );
  }

  public onClick(): void {
    window.location.href = `${this._environment.aplazo.landingpage}`;
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
