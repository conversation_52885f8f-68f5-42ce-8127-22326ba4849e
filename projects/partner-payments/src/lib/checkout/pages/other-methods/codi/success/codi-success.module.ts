import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatRadioModule } from '@angular/material/radio';

import { WebUiModule } from '@aplazo/web-ui';

import { CodiSuccessRoutingModule } from './codi-success-routing.module';
import { CodiSuccessComponent } from './codi-success.component';
import { ComponentsModule } from '../../../../components/components.module';

@NgModule({
  declarations: [
    CodiSuccessComponent
  ],
  imports: [
    CommonModule,
    CodiSuccessRoutingModule,
    MatRadioModule,
    WebUiModule,
    ComponentsModule
  ]
})
export class CodiSuccessModule { }
