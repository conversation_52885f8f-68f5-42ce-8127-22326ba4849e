import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ErrorPages, OtherPaymentMethods } from '../../enums/pages.enum';

const routes: Routes = [
  {
    path: 'codi',
    loadChildren: () => import('./codi/codi.module').then(m => m.CodiModule),
    data: {
      code: OtherPaymentMethods.CODI,
    },
  },
  {
    path: 'codi-success',
    loadChildren: () => import('./codi/success/codi-success.module').then(m => m.CodiSuccessModule),
    data: {
      code: OtherPaymentMethods.CODI_SUCCESS,
    },
  },
  {
    path: '**',
    redirectTo: ErrorPages.SERVER,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OtherMethodsRoutingModule { }
