import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Pages } from '../enums/pages.enum';

const routes: Routes = [
  {
    path: Pages.selectPlan, // select plan
    loadChildren: () => import('./select-installment/select-installment.module').then(m => m.SelectInstallmentModule),
  },
  {
    path: Pages.planConfirmation, // checkout
    loadChildren: () => import('./plan-confirmation/plan-confirmation.module').then(m => m.PlanConfirmationModule),
  },
  {
    path: 'walmart-cashi-payment/checkout', // Cashi flow - redirect to orchestrator first
    redirectTo: '',
    pathMatch: 'full',
  },
  {
    path: Pages.error, // error
    loadChildren: () => import('./errors/errors.module').then(m => m.ErrorsModule),
  },
  {
    path: Pages.thankyou, // thankyou
    loadChildren: () => import('./thankyou/thankyou.module').then(m => m.ThankyouModule),
  },
  {
    path: Pages.other, // Other payment methods flow
    loadChildren: () => import('./other-methods/other-methods.module').then(m => m.OtherMethodsModule),
  },
  {
    path: Pages.default, // '' (empty)
    loadChildren: () =>
      import('./payment-orchestrator/payment-orchestrator.module').then(m => m.PaymentOrchestratorModule),
  },
  {
    path: '**',
    redirectTo: Pages.default,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PagesRoutingModule {}
