import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';

import { PaymentOrchestratorComponent } from './payment-orchestrator.component';
import { IScheme } from '../../interfaces/checkout-info.interface';
import { UiCoreFacade } from '@aplazo/partner-core';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { ErrorsHandlerService } from '../../services/errors-handler/errors-handler.service';
import { CustomerFacade } from '@aplazo/partner-core';

describe('PaymentOrchestratorComponent', () => {
  let component: PaymentOrchestratorComponent;
  let fixture: ComponentFixture<PaymentOrchestratorComponent>;
  let mockActivatedRoute: any;
  let mockRouter: any;
  let mockStore: any;
  let mockUiCoreFacade: any;
  let mockPagesFacade: any;
  let mockCheckoutInfoFacade: any;
  let mockSchemeFacade: any;
  let mockErrorsHandlerService: any;
  let mockCustomerFacade: any;

  const mockSchemes: IScheme[] = [
    { 
      id: 1, 
      installments: 3, 
      commission: 100, 
      fee_amount: 50, 
      total: 150, 
      total_first_installment: 50, 
      first_installment_extra_amount: null,
      ghost_commission_amount: null,
      interest_amount: 0,
      total_discount_amount: 0,
      first_installment: 50,
      calendar: [], 
      customer_discounts_applied: [] 
    },
    { 
      id: 2, 
      installments: 6, 
      commission: 200, 
      fee_amount: 100, 
      total: 300, 
      total_first_installment: 100, 
      first_installment_extra_amount: null,
      ghost_commission_amount: null,
      interest_amount: 0,
      total_discount_amount: 0,
      first_installment: 100,
      calendar: [], 
      customer_discounts_applied: [] 
    },
    { 
      id: 3, 
      installments: 12, 
      commission: 400, 
      fee_amount: 200, 
      total: 600, 
      total_first_installment: 200, 
      first_installment_extra_amount: null,
      ghost_commission_amount: null,
      interest_amount: 0,
      total_discount_amount: 0,
      first_installment: 200,
      calendar: [], 
      customer_discounts_applied: [] 
    },
    { 
      id: -1, 
      installments: 1, 
      commission: 0, 
      fee_amount: 0, 
      total: 100, 
      total_first_installment: 100, 
      first_installment_extra_amount: null,
      ghost_commission_amount: null,
      interest_amount: 0,
      total_discount_amount: 0,
      first_installment: 100,
      calendar: [], 
      customer_discounts_applied: [] 
    }
  ];

  beforeEach(async () => {
    mockActivatedRoute = {
      snapshot: {
        queryParams: {}
      }
    };

    mockRouter = {
      navigate: jasmine.createSpy('navigate')
    };

    mockStore = {
      dispatch: jasmine.createSpy('dispatch'),
      pipe: jasmine.createSpy('pipe').and.returnValue(of({})),
      select: jasmine.createSpy('select').and.returnValue(of({}))
    };

    mockUiCoreFacade = {
      setLoading: jasmine.createSpy('setLoading'),
      setShowButtonCancel: jasmine.createSpy('setShowButtonCancel')
    };

    mockPagesFacade = {
      setCurrentPage: jasmine.createSpy('setCurrentPage'),
      setCurrentUrls: jasmine.createSpy('setCurrentUrls')
    };

    mockCheckoutInfoFacade = {
      getInfoCheckout: jasmine.createSpy('getInfoCheckout'),
      checkoutInfo$: of({})
    };

    mockSchemeFacade = {
      setInfoCheckout: jasmine.createSpy('setInfoCheckout'),
      getSchemeSelected$: of({})
    };

    mockErrorsHandlerService = {
      errorHandler: jasmine.createSpy('errorHandler')
    };

    mockCustomerFacade = {
      getMe: jasmine.createSpy('getMe'),
      me$: of({})
    };

    await TestBed.configureTestingModule({
      declarations: [ PaymentOrchestratorComponent ],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: Store, useValue: mockStore },
        { provide: UiCoreFacade, useValue: mockUiCoreFacade },
        { provide: PagesFacade, useValue: mockPagesFacade },
        { provide: CheckoutInfoFacade, useValue: mockCheckoutInfoFacade },
        { provide: SchemeFacade, useValue: mockSchemeFacade },
        { provide: ErrorsHandlerService, useValue: mockErrorsHandlerService },
        { provide: CustomerFacade, useValue: mockCustomerFacade }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentOrchestratorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Query Parameter Extraction', () => {
    it('should extract valid installments parameter', () => {
      mockActivatedRoute.snapshot.queryParams = { installments: '6' };
      const result = component['getQueryInstallments']();
      expect(result).toBe(6);
    });

    it('should return null for missing installments parameter', () => {
      mockActivatedRoute.snapshot.queryParams = {};
      const result = component['getQueryInstallments']();
      expect(result).toBeNull();
    });

    it('should return null for invalid installments parameter', () => {
      mockActivatedRoute.snapshot.queryParams = { installments: 'invalid' };
      const result = component['getQueryInstallments']();
      expect(result).toBeNull();
    });

    it('should return null for negative installments parameter', () => {
      mockActivatedRoute.snapshot.queryParams = { installments: '-5' };
      const result = component['getQueryInstallments']();
      expect(result).toBeNull();
    });

    it('should return null for zero installments parameter', () => {
      mockActivatedRoute.snapshot.queryParams = { installments: '0' };
      const result = component['getQueryInstallments']();
      expect(result).toBeNull();
    });

    it('should extract valid offerId parameter', () => {
      mockActivatedRoute.snapshot.queryParams = { offerId: '123' };
      const result = component['getQueryOfferId']();
      expect(result).toBe('123');
    });

    it('should return null for missing offerId parameter', () => {
      mockActivatedRoute.snapshot.queryParams = {};
      const result = component['getQueryOfferId']();
      expect(result).toBeNull();
    });

    it('should trim whitespace from offerId parameter', () => {
      mockActivatedRoute.snapshot.queryParams = { offerId: '  123  ' };
      const result = component['getQueryOfferId']();
      expect(result).toBe('123');
    });
  });

  describe('Scheme Matching', () => {
    it('should find matching scheme by installments', () => {
      const result = component['getSchemeByInstallments'](mockSchemes, 6);
      expect(result).toBeTruthy();
      expect(result?.id).toBe(2);
      expect(result?.installments).toBe(6);
    });

    it('should return null when no matching scheme found', () => {
      const result = component['getSchemeByInstallments'](mockSchemes, 24);
      expect(result).toBeNull();
    });

    it('should return null for empty schemes array', () => {
      const result = component['getSchemeByInstallments']([], 6);
      expect(result).toBeNull();
    });

    it('should return null for null schemes array', () => {
      const result = component['getSchemeByInstallments'](null as any, 6);
      expect(result).toBeNull();
    });

    it('should select scheme with lowest ID when multiple matches', () => {
      const schemesWithDuplicates = [
        ...mockSchemes,
        { 
          id: 5, 
          installments: 6, 
          commission: 250, 
          fee_amount: 125, 
          total: 375, 
          total_first_installment: 125, 
          first_installment_extra_amount: null,
          ghost_commission_amount: null,
          interest_amount: 0,
          total_discount_amount: 0,
          first_installment: 125,
          calendar: [], 
          customer_discounts_applied: [] 
        }
      ];
      const result = component['getSchemeByInstallments'](schemesWithDuplicates, 6);
      expect(result?.id).toBe(2); // Should select the one with lower ID
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain existing behavior when no query parameters', () => {
      mockActivatedRoute.snapshot.queryParams = {};
      // This test would need to be expanded with proper mocking of facades
      // For now, we verify the methods exist and can be called
      expect(component['getQueryInstallments']()).toBeNull();
      expect(component['getQueryOfferId']()).toBeNull();
    });

    it('should fallback to current logic when preselection fails', () => {
      mockActivatedRoute.snapshot.queryParams = { installments: '999' }; // Non-existent installments
      const result = component['getSchemeByInstallments'](mockSchemes, 999);
      expect(result).toBeNull();
    });
  });
});
