import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CustomerFacade, UiCoreFacade } from '@aplazo/partner-core';
import { filter, Subject, takeUntil, withLatestFrom } from 'rxjs';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { Pages } from '../../enums/pages.enum';
import { IScheme } from '../../interfaces/checkout-info.interface';
import { ErrorsHandlerService } from '../../services/errors-handler/errors-handler.service';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { ActivatedRoute, Router } from '@angular/router';
import { IPage } from '../../interfaces/page.interface';

@Component({
  selector: 'aplazo-payments-payment-orchestrator',
  template: '<p>Cargando...</p>',
})
export class PaymentOrchestratorComponent implements OnInit, OnDestroy {
  private _unsubscribe$: Subject<boolean>;

  constructor(
    private _uiFacade: UiCoreFacade,
    private _pagesFacade: PagesFacade,
    private readonly _checkoutInfoFacade: CheckoutInfoFacade,
    private _errorsHandlerService: ErrorsHandlerService,
    private _schemeFacade: SchemeFacade,
    private _route: ActivatedRoute,
    private _router: Router,
    private _customerFacade: CustomerFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._unsubscribe$ = new Subject<boolean>();

    const token = localStorage.getItem('TOKEN_LOAN');
    if (!token) {
      void this._router.navigate(['not-found']);
      return;
    }
    this._uiFacade.setShowButtonCancel(false);
    this._customerFacade.getMe();
    this._checkoutInfoFacade.getInfoCheckout(token);
    this._checkoutInfoFacade.checkoutInfo$
      .pipe(
        filter(data => !!data),
        withLatestFrom(this._route.data),
        takeUntil(this._unsubscribe$)
      )
      .subscribe({
        next: ([data, routeData]) => {
          const { currentUrl, currentUrlLogin } = routeData as IPage;
          this._pagesFacade.setCurrentUrls(currentUrl, currentUrlLogin);
          if (data.error) {
            const { error } = data;
            this._errorsHandlerService.errorHandler(error);
          } else {
            const queryInstallments = this.getQueryInstallments();
            const queryOfferId = this.getQueryOfferId();
            const queryType = this.getQueryType();

            if (queryInstallments) {
              if (queryOfferId && queryType === 'original') {
                const matchedScheme = this.getSchemeByInstallments(data.schemes, queryInstallments);
                if (matchedScheme) {
                  this._pagesFacade.setCurrentPage(Pages.planConfirmation);
                  this._schemeFacade.setInfoCheckout(matchedScheme);
                  return;
                }
              }
              else if (!queryOfferId && !queryType) {
                const matchedScheme = this.getSchemeByInstallments(data.schemes, queryInstallments);
                if (matchedScheme) {
                  this._pagesFacade.setCurrentPage(Pages.planConfirmation);
                  this._schemeFacade.setInfoCheckout(matchedScheme);
                  return;
                }
              }
            }

            if (this.hasDynamicInstallments(data.schemes)) {
              this._pagesFacade.setCurrentPage(Pages.selectPlan);
            } else {
              this._pagesFacade.setCurrentPage(Pages.planConfirmation);
              this._schemeFacade.setInfoCheckout(data.schemes[0]);
            }
          }
        },
        error: err => {
          this._errorsHandlerService.errorHandler(err);
        },
      });
  }

  private hasDynamicInstallments(schemes: IScheme[]): boolean {
    return schemes.length > 1;
  }

  /**
   * Extracts and validates the installments parameter from URL query parameters
   * @returns The validated installments number or null if invalid/missing
   */
  private getQueryInstallments(): number | null {
    try {
      const params = this._route.snapshot.queryParams;
      const installmentsParam = params['installments'];

      if (installmentsParam) {
        const installments = parseInt(installmentsParam, 10);
        if (!isNaN(installments) && installments > 0) {
          return installments;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Extracts the offerId parameter from URL query parameters
   * @returns The offerId string or null if not present
   */
  private getQueryOfferId(): string | null {
    try {
      const params = this._route.snapshot.queryParams;
      const offerId = params['offerId'];

      if (offerId && typeof offerId === 'string' && offerId.trim().length > 0) {
        return offerId.trim();
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Extracts the type parameter from URL query parameters
   * @returns The type string ('original' or 'alternative') or null if not present
   */
  private getQueryType(): string | null {
    try {
      const params = this._route.snapshot.queryParams;
      const type = params['type'];

      if (type && typeof type === 'string') {
        const normalizedType = type.trim().toLowerCase();
        if (normalizedType === 'original' || normalizedType === 'alternative') {
          return normalizedType;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Finds a scheme by the specified number of installments
   * @param schemes Array of available schemes
   * @param installments Number of installments to match
   * @returns The matching scheme or null if not found
   */
  private getSchemeByInstallments(schemes: IScheme[], installments: number): IScheme | null {
    try {
      if (!Array.isArray(schemes) || schemes.length === 0) {
        return null;
      }

      const matchingSchemes = schemes.filter(scheme => scheme.installments === installments);

      if (matchingSchemes.length === 0) {
        return null;
      }

      if (matchingSchemes.length === 1) {
        return matchingSchemes[0];
      }

      const selectedScheme = matchingSchemes.reduce((prev, current) => (prev.id < current.id ? prev : current));
      return selectedScheme;
    } catch (error) {
      return null;
    }
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
