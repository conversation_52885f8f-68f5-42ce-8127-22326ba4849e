import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CustomerFacade, UiCoreFacade } from '@aplazo/partner-core';
import { filter, Subject, takeUntil, withLatestFrom } from 'rxjs';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { Pages } from '../../enums/pages.enum';
import { IScheme } from '../../interfaces/checkout-info.interface';
import { ErrorsHandlerService } from '../../services/errors-handler/errors-handler.service';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { ActivatedRoute, Router } from '@angular/router';
import { IPage } from '../../interfaces/page.interface';

interface QueryParams {
  installments?: string;
  offerId?: string;
}

@Component({
  selector: 'aplazo-payments-payment-orchestrator',
  template: '<p>Cargando...</p>',
})
export class PaymentOrchestratorComponent implements OnInit, OnDestroy {
  private _unsubscribe$: Subject<boolean>;

  constructor(
    private _uiFacade: UiCoreFacade,
    private _pagesFacade: PagesFacade,
    private readonly _checkoutInfoFacade: CheckoutInfoFacade,
    private _errorsHandlerService: ErrorsHandlerService,
    private _schemeFacade: SchemeFacade,
    private _route: ActivatedRoute,
    private _router: Router,
    private _customerFacade: CustomerFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._unsubscribe$ = new Subject<boolean>();

    // Check if we're coming from Cashi flow and store parameters
    this.handleCashiFlow();

    const token = localStorage.getItem('TOKEN_LOAN');
    if (!token) {
      void this._router.navigate(['not-found']);
      return;
    }
    this._uiFacade.setShowButtonCancel(false);
    this._customerFacade.getMe();
    this._checkoutInfoFacade.getInfoCheckout(token);
    this._checkoutInfoFacade.checkoutInfo$
      .pipe(
        filter(data => !!data),
        withLatestFrom(this._route.data),
        takeUntil(this._unsubscribe$)
      )
      .subscribe({
        next: ([data, routeData]) => {
          const { currentUrl, currentUrlLogin } = routeData as IPage;
          this._pagesFacade.setCurrentUrls(currentUrl, currentUrlLogin);
          if (data.error) {
            const { error } = data;
            this._errorsHandlerService.errorHandler(error);
          } else {
            // Check for query parameters first
            const queryInstallments = this.getQueryInstallments();
            const queryOfferId = this.getQueryOfferId();

            console.log('🔍 Query parameters:', { queryInstallments, queryOfferId });
            console.log('🔍 Available schemes:', data.schemes);

            if (queryInstallments) {
              const matchedScheme = this.getSchemeByInstallments(data.schemes, queryInstallments);
              this.logSchemePreselection(queryInstallments, data.schemes, matchedScheme);

              if (matchedScheme) {
                // Preselection successful: skip to plan confirmation
                console.log('✅ Preselection successful, going to plan confirmation');
                this._pagesFacade.setCurrentPage(Pages.planConfirmation);
                this._schemeFacade.setInfoCheckout(matchedScheme);
                return;
              } else {
                console.warn('❌ No matching scheme found, falling back to normal flow');
              }
            }

            // Current logic (fallback or when no query parameters)
            if (this.hasDynamicInstallments(data.schemes)) {
              console.log('🔄 Using dynamic installments flow');
              this._pagesFacade.setCurrentPage(Pages.selectPlan);
            } else {
              console.log('🔄 Using single scheme flow');
              this._pagesFacade.setCurrentPage(Pages.planConfirmation);
              this._schemeFacade.setInfoCheckout(data.schemes[0]);
            }
          }
        },
        error: err => {
          console.error(err);
          this._errorsHandlerService.errorHandler(err);
        },
      });
  }

  private hasDynamicInstallments(schemes: IScheme[]): boolean {
    return !schemes.some(scheme => scheme.id === -1) && schemes.length > 1;
  }

  /**
   * Handles Cashi flow by detecting if we're coming from walmart-cashi-payment
   * and storing parameters for later use
   */
  private handleCashiFlow(): void {
    const currentUrl = this._router.url;
    console.log('🔍 PaymentOrchestratorComponent - Current URL:', currentUrl);

    // Check if we're coming from Cashi flow
    if (currentUrl.includes('walmart-cashi-payment')) {
      console.log('🔍 Cashi flow detected in PaymentOrchestratorComponent');

      // Try to get parameters from the original URL (before redirection)
      // This is a workaround since Cashi loses query params during redirection
      const referrer = document.referrer;
      console.log('🔍 Document referrer:', referrer);

      if (referrer && referrer.includes('walmart-cashi-payment')) {
        try {
          const url = new URL(referrer);
          const installments = url.searchParams.get('installments');
          const offerId = url.searchParams.get('offerId');

          if (installments || offerId) {
            console.log('🔍 Found Cashi parameters in referrer:', { installments, offerId });

            // Store parameters for later use
            if (installments) {
              sessionStorage.setItem('cashi-installments', installments);
            }
            if (offerId) {
              sessionStorage.setItem('cashi-offerId', offerId);
            }
          }
        } catch (error) {
          console.error('Error parsing referrer URL:', error);
        }
      }
    }
  }

  /**
   * Extracts and validates the installments parameter from URL query parameters
   * @returns The validated installments number or null if invalid/missing
   */
  private getQueryInstallments(): number | null {
    try {
      // First try query parameters
      const params = this._route.snapshot.queryParams;
      const installmentsParam = params['installments'];

      if (installmentsParam) {
        const installments = parseInt(installmentsParam, 10);
        if (!isNaN(installments) && installments > 0) {
          return installments;
        }
        console.warn('Invalid installments parameter:', installmentsParam);
      }

      // Fallback to stored Cashi parameters
      const storedInstallments = sessionStorage.getItem('cashi-installments');
      if (storedInstallments) {
        const installments = parseInt(storedInstallments, 10);
        if (!isNaN(installments) && installments > 0) {
          console.log('🔍 Using stored Cashi installments:', installments);
          return installments;
        }
      }

      return null;
    } catch (error) {
      console.error('Error extracting installments parameter:', error);
      return null;
    }
  }

  /**
   * Finds a scheme by the specified number of installments
   * @param schemes Array of available schemes
   * @param installments Number of installments to match
   * @returns The matching scheme or null if not found
   */
  private getSchemeByInstallments(schemes: IScheme[], installments: number): IScheme | null {
    try {
      if (!Array.isArray(schemes) || schemes.length === 0) {
        console.warn('No schemes available for matching');
        return null;
      }

      console.log(`🔍 Looking for scheme with ${installments} installments`);
      console.log(
        `🔍 Available schemes:`,
        schemes.map(s => ({ id: s.id, installments: s.installments }))
      );

      const matchingSchemes = schemes.filter(scheme => scheme.installments === installments);

      console.log('🔍 Matching schemes:', matchingSchemes);

      if (matchingSchemes.length === 0) {
        console.warn(`❌ No scheme found for ${installments} installments`);
        console.log(
          `Available installments:`,
          schemes.map(s => s.installments)
        );
        return null;
      }

      if (matchingSchemes.length === 1) {
        console.log(`✅ Found single matching scheme:`, matchingSchemes[0]);
        return matchingSchemes[0];
      }

      // Multiple matches: select by lowest ID
      const selectedScheme = matchingSchemes.reduce((prev, current) => (prev.id < current.id ? prev : current));

      console.log(
        `✅ Multiple schemes found for ${installments} installments, selected scheme ID: ${selectedScheme.id}`
      );
      return selectedScheme;
    } catch (error) {
      console.error('Error matching scheme by installments:', error);
      return null;
    }
  }

  /**
   * Extracts the offerId parameter from URL query parameters
   * @returns The offerId string or null if not present
   */
  private getQueryOfferId(): string | null {
    try {
      // First try query parameters
      const params = this._route.snapshot.queryParams;
      const offerId = params['offerId'];

      if (offerId && typeof offerId === 'string' && offerId.trim().length > 0) {
        console.log('OfferId parameter captured:', offerId);
        return offerId.trim();
      }

      // Fallback to stored Cashi parameters
      const storedOfferId = sessionStorage.getItem('cashi-offerId');
      if (storedOfferId && storedOfferId.trim().length > 0) {
        console.log('🔍 Using stored Cashi offerId:', storedOfferId);
        return storedOfferId.trim();
      }

      return null;
    } catch (error) {
      console.error('Error extracting offerId parameter:', error);
      return null;
    }
  }

  /**
   * Logs scheme preselection process for debugging
   * @param installments Requested number of installments
   * @param schemes Available schemes array
   * @param result Matched scheme or null
   */
  private logSchemePreselection(installments: number, schemes: IScheme[], result: IScheme | null): void {
    const logData = {
      timestamp: new Date().toISOString(),
      requestedInstallments: installments,
      availableSchemes: schemes.length,
      availableInstallments: schemes.map(s => s.installments),
      matchedScheme: result?.id || 'none',
      fallbackUsed: result === null,
      preselectionSuccess: result !== null,
    };

    console.log('Scheme Preselection:', logData);
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
