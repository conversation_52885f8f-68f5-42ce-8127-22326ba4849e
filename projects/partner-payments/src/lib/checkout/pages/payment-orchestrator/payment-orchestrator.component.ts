import { Component, OnDestroy, OnInit } from '@angular/core';
import { CustomerFacade, UiCoreFacade } from '@aplazo/partner-core';
import { filter, Subject, takeUntil, withLatestFrom } from 'rxjs';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { Pages } from '../../enums/pages.enum';
import { IScheme, ICheckoutInfo, IAlternativeOffer } from '../../interfaces/checkout-info.interface';
import { ErrorsHandlerService } from '../../services/errors-handler/errors-handler.service';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { ActivatedRoute, Router } from '@angular/router';
import { IPage } from '../../interfaces/page.interface';

interface QueryParams {
  installments?: string;
  offerId?: string;
}

@Component({
  selector: 'aplazo-payments-payment-orchestrator',
  template: '<p>Cargando...</p>',
})
export class PaymentOrchestratorComponent implements OnInit, OnDestroy {
  private _unsubscribe$: Subject<boolean>;

  constructor(
    private _uiFacade: UiCoreFacade,
    private _pagesFacade: PagesFacade,
    private readonly _checkoutInfoFacade: CheckoutInfoFacade,
    private _errorsHandlerService: ErrorsHandlerService,
    private _schemeFacade: SchemeFacade,
    private _route: ActivatedRoute,
    private _router: Router,
    private _customerFacade: CustomerFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._unsubscribe$ = new Subject<boolean>();

    // Check if we're coming from Cashi flow and store parameters
    this.handleCashiFlow();

    const token = localStorage.getItem('TOKEN_LOAN');
    if (!token) {
      void this._router.navigate(['not-found']);
      return;
    }
    this._uiFacade.setShowButtonCancel(false);
    this._customerFacade.getMe();
    this._checkoutInfoFacade.getInfoCheckout(token);
    this._checkoutInfoFacade.checkoutInfo$
      .pipe(
        filter(data => !!data),
        withLatestFrom(this._route.data),
        takeUntil(this._unsubscribe$)
      )
      .subscribe({
        next: ([data, routeData]) => {
          const { currentUrl, currentUrlLogin } = routeData as IPage;
          this._pagesFacade.setCurrentUrls(currentUrl, currentUrlLogin);
          if (data.error) {
            const { error } = data;
            this._errorsHandlerService.errorHandler(error);
          } else {
            // Check for query parameters first
            const queryInstallments = this.getQueryInstallments();
            const queryOfferId = this.getQueryOfferId();

            if (queryInstallments && queryOfferId) {
              // First check if offerId corresponds to original_offer
              if (data.original_offer?.offer_id === queryOfferId) {
                const originalScheme = this.getSchemeByInstallments(data.original_offer.schemes, queryInstallments);
                if (originalScheme) {
                  this._pagesFacade.setCurrentPage(Pages.planConfirmation);
                  this._schemeFacade.setInfoCheckout(originalScheme);
                  return;
                }
              }

              // Second check if offerId corresponds to an alternative offer
              const alternativeScheme = this.getSchemeFromAlternativeOffer(data, queryOfferId, queryInstallments);
              if (alternativeScheme) {
                this._pagesFacade.setCurrentPage(Pages.planConfirmation);
                this._schemeFacade.setInfoCheckout(alternativeScheme);
                return;
              }

              // If offerId is provided but not found in either original or alternative offers
              console.warn(`OfferId ${queryOfferId} not found in original_offer or alternative_offers`);
            }

            // Fallback: Only installments provided (without specific offerId)
            if (queryInstallments) {
              const matchedScheme = this.getSchemeByInstallments(data.schemes, queryInstallments);

              if (matchedScheme) {
                this._pagesFacade.setCurrentPage(Pages.planConfirmation);
                this._schemeFacade.setInfoCheckout(matchedScheme);
                return;
              }
            }

            if (this.hasDynamicInstallments(data.schemes)) {
              this._pagesFacade.setCurrentPage(Pages.selectPlan);
            } else {
              this._pagesFacade.setCurrentPage(Pages.planConfirmation);
              this._schemeFacade.setInfoCheckout(data.schemes[0]);
            }
          }
        },
        error: err => {
          this._errorsHandlerService.errorHandler(err);
        },
      });
  }

  private hasDynamicInstallments(schemes: IScheme[]): boolean {
    return !schemes.some(scheme => scheme.id === -1) && schemes.length > 1;
  }

  /**
   * Handles Cashi flow by detecting if we're coming from walmart-cashi-payment
   * and storing parameters for later use
   */
  private handleCashiFlow(): void {
    const currentUrl = this._router.url;

    if (currentUrl.includes('walmart-cashi-payment')) {
      const referrer = document.referrer;

      if (referrer && referrer.includes('walmart-cashi-payment')) {
        try {
          const url = new URL(referrer);
          const installments = url.searchParams.get('installments');
          const offerId = url.searchParams.get('offerId');

          if (installments || offerId) {
            if (installments) {
              sessionStorage.setItem('cashi-installments', installments);
            }
            if (offerId) {
              sessionStorage.setItem('cashi-offerId', offerId);
            }
          }
        } catch (error) {
          // Silent error handling
        }
      }
    }
  }

  /**
   * Extracts and validates the installments parameter from URL query parameters
   * @returns The validated installments number or null if invalid/missing
   */
  private getQueryInstallments(): number | null {
    try {
      // First try query parameters
      const params = this._route.snapshot.queryParams;
      const installmentsParam = params['installments'];

      if (installmentsParam) {
        const installments = parseInt(installmentsParam, 10);
        if (!isNaN(installments) && installments > 0) {
          return installments;
        }
      }

      const storedInstallments = sessionStorage.getItem('cashi-installments');
      if (storedInstallments) {
        const installments = parseInt(storedInstallments, 10);
        if (!isNaN(installments) && installments > 0) {
          return installments;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Finds a scheme by the specified number of installments
   * @param schemes Array of available schemes
   * @param installments Number of installments to match
   * @returns The matching scheme or null if not found
   */
  private getSchemeByInstallments(schemes: IScheme[], installments: number): IScheme | null {
    try {
      if (!Array.isArray(schemes) || schemes.length === 0) {
        return null;
      }

      const matchingSchemes = schemes.filter(scheme => scheme.installments === installments);

      if (matchingSchemes.length === 0) {
        return null;
      }

      if (matchingSchemes.length === 1) {
        return matchingSchemes[0];
      }

      const selectedScheme = matchingSchemes.reduce((prev, current) => (prev.id < current.id ? prev : current));
      return selectedScheme;
    } catch (error) {
      return null;
    }
  }

  /**
   * Gets scheme from alternative offers based on offerId and installments
   * @param data Checkout info data
   * @param offerId Offer ID from query parameters
   * @param installments Number of installments from query parameters
   * @returns IScheme if found, null otherwise
   */
  private getSchemeFromAlternativeOffer(data: ICheckoutInfo, offerId: string, installments: number): IScheme | null {
    try {
      if (!data.alternative_offers || !Array.isArray(data.alternative_offers)) {
        return null;
      }

      const matchingOffer = data.alternative_offers.find(
        (offer: IAlternativeOffer) => offer.offer_id === offerId && offer.installments === installments
      );

      if (!matchingOffer) {
        return null;
      }

      // Convert alternative offer to IScheme format
      return this.convertAlternativeOfferToScheme(matchingOffer);
    } catch (error) {
      return null;
    }
  }

  /**
   * Converts an alternative offer to IScheme format
   * @param offer Alternative offer object
   * @returns IScheme object
   */
  private convertAlternativeOfferToScheme(offer: IAlternativeOffer): IScheme {
    return {
      id: -2, // Special ID for alternative offers
      installments: offer.installments,
      fee_amount: offer.fee_amount,
      total: offer.total,
      commission: 0, // Not provided in alternative offer
      interest_amount: 0, // Not provided in alternative offer
      total_first_installment: offer.fee_amount, // Assuming first installment equals fee_amount
      first_installment_extra_amount: null,
      ghost_commission_amount: null,
      total_discount_amount: 0,
      first_installment: offer.fee_amount,
      iva_amount_plus_commission_amount: offer.iva_amount_plus_commission_amount,
      calendar: [], // Empty calendar for alternative offers
      customer_discounts_applied: [], // No discounts for alternative offers
      iva_amount_from_commission_amount: undefined,
      iva_rate: undefined,
      iva_rate_plus_commission_rate: undefined,
      upfront_rate: undefined,
      aplazo_points_amounts: undefined,
    };
  }

  /**
   * Extracts the offerId parameter from URL query parameters
   * @returns The offerId string or null if not present
   */
  private getQueryOfferId(): string | null {
    try {
      // First try query parameters
      const params = this._route.snapshot.queryParams;
      const offerId = params['offerId'];

      if (offerId && typeof offerId === 'string' && offerId.trim().length > 0) {
        return offerId.trim();
      }

      const storedOfferId = sessionStorage.getItem('cashi-offerId');
      if (storedOfferId && storedOfferId.trim().length > 0) {
        return storedOfferId.trim();
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
