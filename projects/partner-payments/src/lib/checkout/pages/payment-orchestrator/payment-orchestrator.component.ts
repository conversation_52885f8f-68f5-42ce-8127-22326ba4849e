import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CustomerFacade, UiCoreFacade } from '@aplazo/partner-core';
import { filter, Subject, takeUntil, withLatestFrom } from 'rxjs';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { Pages } from '../../enums/pages.enum';
import { IScheme } from '../../interfaces/checkout-info.interface';
import { ErrorsHandlerService } from '../../services/errors-handler/errors-handler.service';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { ActivatedRoute, Router } from '@angular/router';
import { IPage } from '../../interfaces/page.interface';

@Component({
  selector: 'aplazo-payments-payment-orchestrator',
  template: '<p>Cargando...</p>',
})
export class PaymentOrchestratorComponent implements OnInit, OnDestroy {
  private _unsubscribe$: Subject<boolean>;

  constructor(
    private _uiFacade: UiCoreFacade,
    private _pagesFacade: PagesFacade,
    private readonly _checkoutInfoFacade: CheckoutInfoFacade,
    private _errorsHandlerService: ErrorsHandlerService,
    private _schemeFacade: SchemeFacade,
    private _route: ActivatedRoute,
    private _router: Router,
    private _customerFacade: CustomerFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._unsubscribe$ = new Subject<boolean>();

    // Check if we're coming from Cashi flow and store parameters
    this.handleCashiFlow();

    const token = localStorage.getItem('TOKEN_LOAN');
    if (!token) {
      void this._router.navigate(['not-found']);
      return;
    }
    this._uiFacade.setShowButtonCancel(false);
    this._customerFacade.getMe();
    this._checkoutInfoFacade.getInfoCheckout(token);
    this._checkoutInfoFacade.checkoutInfo$
      .pipe(
        filter(data => !!data),
        withLatestFrom(this._route.data),
        takeUntil(this._unsubscribe$)
      )
      .subscribe({
        next: ([data, routeData]) => {
          const { currentUrl, currentUrlLogin } = routeData as IPage;
          this._pagesFacade.setCurrentUrls(currentUrl, currentUrlLogin);
          if (data.error) {
            const { error } = data;
            this._errorsHandlerService.errorHandler(error);
          } else {
            if (this.hasDynamicInstallments(data.schemes)) {
              this._pagesFacade.setCurrentPage(Pages.selectPlan);
            } else {
              this._pagesFacade.setCurrentPage(Pages.planConfirmation);
              this._schemeFacade.setInfoCheckout(data.schemes[0]);
            }
          }
        },
        error: err => {
          this._errorsHandlerService.errorHandler(err);
        },
      });
  }

  private hasDynamicInstallments(schemes: IScheme[]): boolean {
    return schemes.length > 1;
  }

  /**
   * Handles Cashi flow by detecting if we're coming from walmart-cashi-payment
   * and storing parameters for later use
   */
  private handleCashiFlow(): void {
    const currentUrl = this._router.url;

    if (currentUrl.includes('walmart-cashi-payment')) {
      const referrer = document.referrer;

      if (referrer && referrer.includes('walmart-cashi-payment')) {
        try {
          const url = new URL(referrer);
          const installments = url.searchParams.get('installments');
          const offerId = url.searchParams.get('offerId');

          if (installments || offerId) {
            if (installments) {
              sessionStorage.setItem('cashi-installments', installments);
            }
            if (offerId) {
              sessionStorage.setItem('cashi-offerId', offerId);
            }
          }
        } catch (error) {
          // Silent error handling
        }
      }
    }
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
