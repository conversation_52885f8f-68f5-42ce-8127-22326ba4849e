<aplazo-payments-plan-confirmation-template
  *ngIf="txtTemplate && checkoutInfo"
  [txtTemplate]="txtTemplate"
  [creditLimit]="checkoutInfo.credit_available"
  [subtotal]="checkoutInfo.purchase_amount"
  [discounts]="discounts"
  [total]="useAplazoPoints ? schemeSelected.aplazo_points_amounts.total : schemeSelected.total"
  [commission]="useAplazoPoints ? schemeSelected.aplazo_points_amounts.interest_amount : schemeSelected.interest_amount"
  [totalDiscountAmount]="
    useAplazoPoints ? schemeSelected.aplazo_points_amounts.subtotal : schemeSelected.total_discount_amount
  "
  [feeCustomer]="useAplazoPoints ? schemeSelected.aplazo_points_amounts.commission : schemeSelected.commission"
  [extraAmountQuantity]="
    useAplazoPoints
      ? schemeSelected.aplazo_points_amounts.first_installment_extra_amount
      : schemeSelected.first_installment_extra_amount
  "
  [ivaAmount]="useAplazoPoints ? schemeSelected.aplazo_points_amounts.iva_amount_from_commission_amount : schemeSelected.iva_amount_from_commission_amount"
  [stepper]="calendar"
  [contactSupportLink]="contactSupportLink | async"
  [showSubtitle]="showButtonChangeInstallment"
  [customerDiscountsEnabled]="promoCodesEnabled"
  [enableAddCoupon]="enableAddPromoCode"
  [validateDiscountCode]="validateDiscountCode"
  [aplazoPointsUsed]="aplazoPointsUsed"
  [showAplazoPointsDiscount]="useAplazoPoints"
  [customerType]="checkoutInfo.customer_type"
  [showModalInfoFirstInstallment]="showModalInfoFirstInstallment"
  (changePlan)="goToSelectPlan()"
  (discountRemoved)="discountRemoved($event)"
  (discountAdded)="discountAdded()"
>
  <section contentFlowStepper class="stepper" *ngIf="isVirtualCard">
    <aplazo-flow-stepper [currentStep]="3" [steps]="3"></aplazo-flow-stepper>
  </section>

  <aplazo-payments-payment-methods
    *ngIf="useAplazoPoints !== null"
    content
    [checkoutInfo]="checkoutInfo"
    [paymentData]="{
      paymentType,
      loanId: checkoutInfo.id,
      schemeId: schemeSelected.id
    }"
    [openAddCardModal]="checkoutInfo.card === null"
    [paymentButtonFixed]="paymentButtonFixed"
    [btnPaymentText]="txtTemplate.btnContinue"
    [btnSubmitDisabled]="isDisabledSubmit"
    [schemeSelected]="schemeSelected"
    [showCheckAplazoPoints]="showCheckAplazoPoints"
    [useAplazoPoints]="useAplazoPoints"
    [forceUseAplazoPoints]="forceUseAplazoPoints"
    [paymentMethodsEnabled]="paymentMethodsEnabled"
    [customerType]="checkoutInfo.customer_type"
    (cardAddedEvent)="updateCheckoutInfo($event)"
    (useAplazoPointsChange)="useAplazoPointsChange($event)"
    [showPromoAplazoPoints]="showPromoAplazoPoints"
    [promoCashback]="promoCashback"
  >
  </aplazo-payments-payment-methods>
</aplazo-payments-plan-confirmation-template>
