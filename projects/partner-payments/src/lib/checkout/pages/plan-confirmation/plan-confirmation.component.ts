import { DOCUMENT } from '@angular/common';
import { Compo<PERSON>, HostListener, Inject, <PERSON><PERSON><PERSON>, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DatalayerService } from '@aplazo/partner-analytics';
import { ContactSupportService, CustomerFacade, ITypeNotification, UiCoreFacade } from '@aplazo/partner-core';
import { IDiscount, IStep } from '@aplazo/web-ui';
import { TranslocoService } from '@ngneat/transloco';
import {
  Observable,
  Subject,
  filter,
  finalize,
  from,
  map,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { PaymentTypes } from '../../../payment-methods/enums/payment-types.enum';
import { DataLayerEventName, DataLayerEventTypes } from '../../enums/datalayer-events.enum';
import { Pages } from '../../enums/pages.enum';
import { translationKey } from '../../helpers/translation-key';
import { CalendarService } from '../../services/calendar/calendar.service';
import { DiscountsService } from '../../services/discounts/discounts.service';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { ICheckoutInfo, IScheme } from '../../interfaces/checkout-info.interface';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { PaymentCheckoutService } from '../../services/payment-checkout/payment-checkout.service';
import { FeatureFlagsService } from '@aplazo/front-feature-flags';
import { PlanConfirmationFacade } from '../../store/facades/plan-confirmation.facade';
import { SPLITS } from '../../../payment-methods/enums/splits.enum';
import { PaymentMethodsEnum } from '../../../payment-methods/enums/payment-methods.enum';
import { PaymentMethodsEnabledService } from '../../../payment-methods/services/utils/set-payment-methods.enabled.service';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { ModalCodiErrorComponent } from '../../components/molecules/modal-codi-error/modal-codi-error.component';
import { CodiService } from '../../../payment-methods/services/codi/codi.service';
import { ErrorsService } from '../../services/errors/errors.service';

@Component({
  selector: 'aplazo-payments-plan-confirmation',
  templateUrl: './plan-confirmation.component.html',
  styleUrls: ['./plan-confirmation.component.scss'],
})
export class PlanConfirmationComponent implements OnInit, OnDestroy {
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  public enableAddPromoCode: boolean = false;

  private _maxWidthButtonFixed = 500;
  private _minWidthButtonFixed = 360;

  public paymentType: PaymentTypes;

  public calendar: IStep[];

  public discounts: IDiscount[] = [];

  public isDisabledSubmit: boolean;

  public txtTemplate: Record<string, string> = null;

  public paymentButtonFixed: boolean;

  public contactSupportLink: Observable<string>;

  public showButtonChangeInstallment: boolean = false;

  public checkoutInfo: ICheckoutInfo = null;

  public schemeSelected: IScheme;

  public canPayNextQuincena: boolean = false;

  public promoCodesEnabled: boolean = false;

  public isDynamicInstallments: boolean = false;

  public showCheckAplazoPoints: boolean = false;
  public useAplazoPoints: boolean = null;
  public forceUseAplazoPoints: boolean = false;
  public aplazoPointsUsed: number = 0;

  public paymentMethodsEnabled: PaymentMethodsEnum[];

  public isVirtualCard: boolean = false;

  public showPromoAplazoPoints: boolean = false;
  public promoCashback: string = null;

  public showModalInfoFirstInstallment: boolean = false;

  private _storedCashiParams: { installments: number | null; offerId: string | null } = {
    installments: null,
    offerId: null,
  };

  constructor(
    @Inject(DOCUMENT) public readonly _document: Document,
    private _calendarService: CalendarService,
    private _uiFacade: UiCoreFacade,
    private _translocoService: TranslocoService,
    private _pagesFacade: PagesFacade,
    private _contactSupportService: ContactSupportService,
    private _datalayerScv: DatalayerService,
    private _discountsService: DiscountsService,
    private _ngZone: NgZone,
    private readonly _checkoutInfoFacade: CheckoutInfoFacade,
    private readonly _schemeFacade: SchemeFacade,
    private _paymentService: PaymentCheckoutService,
    private _featureFlagsService: FeatureFlagsService,
    private _planConfirmationFacade: PlanConfirmationFacade,
    private _customerFacade: CustomerFacade,
    private _paymentMethodsEnabled: PaymentMethodsEnabledService,
    private _dialog: MatDialog,
    private _codiService: CodiService,
    private _errorsService: ErrorsService,
    private _route: ActivatedRoute,
    private _router: Router
  ) {}

  @HostListener('window:resize', ['$event'])
  public onResize(): void {
    this._setPaymentButtonFixedForMobile();
  }

  public ngOnInit(): void {
    if (
      this._router.url.includes('walmart-cashi-payment') &&
      Object.keys(this._route.snapshot.queryParams).length === 0
    ) {
      const storedInstallments = sessionStorage.getItem('cashi-installments');
      const storedOfferId = sessionStorage.getItem('cashi-offerId');

      if (storedInstallments || storedOfferId) {
        this._storedCashiParams = {
          installments: storedInstallments ? parseInt(storedInstallments, 10) : null,
          offerId: storedOfferId,
        };
      }
    }

    this._setPaymentButtonFixedForMobile();
    this.contactSupportLink = from(this._contactSupportService.sendMessage());
    this._datalayerScv.datalayerEvent(
      DataLayerEventName.PLAN_CONFIRMATION,
      DataLayerEventTypes.PAGEVIEW,
      this._document
    );
    this.updateCheckoutInfoAndScheme();
    this._subscribePayment();
    this.codiErrorHandler();
  }

  setMethodPaymentsAvailable(): void {
    this._customerFacade.me$
      .pipe(
        filter(Boolean),
        take(1),
        switchMap(({ id }) => this.processPaymentMethods(id.toString())),
        tap(() => this.setPromoCashback()),
        tap(() => this.setShowModalFlexiCheckout())
      )
      .subscribe({
        next: ff => {
          const paymentMethodsEnabled = Object.entries(ff.config).reduce((acc, [key, value]) => {
            acc[key] = value === 'true';
            return acc;
          }, {} as { [key: string]: boolean });
          this.paymentMethodsEnabled = this._paymentMethodsEnabled.getPaymentMethodsEnabled(paymentMethodsEnabled);
          this._ngZone.run(() => {
            this._uiFacade.setLoading(false);
          });
        },
        error: err => {
          console.error('Error fetching feature flag for payment methods:', err);
          this.paymentMethodsEnabled = this._paymentMethodsEnabled.getPaymentMethodsEnabled({ card: true });
        },
      });
  }

  private setPromoCashback(): void {
    this._featureFlagsService
      .getLoggedFeature$(SPLITS.SPLIT_CASHBACK)
      .pipe(take(1))
      .subscribe({
        next: featureFlagValue => {
          if (featureFlagValue.value) {
            this.promoCashback = featureFlagValue.value.toString();
            this.showPromoAplazoPoints = true;
          }
        },
        error: err => {
          this.showPromoAplazoPoints = false;
        },
      });
  }

  private setShowModalFlexiCheckout(): void {
    this._featureFlagsService
      .getLoggedFeature$(SPLITS.SPLIT_FLEXI_CHECKOUT)
      .pipe(take(1))
      .subscribe({
        next: featureFlagValue => {
          this.showModalInfoFirstInstallment = !!featureFlagValue.value;
        },
        error: err => {
          console.error('Error fetching feature flag:', err);
        },
      });
  }

  private processPaymentMethods(customerId: string): Observable<any> {
    return from(this._featureFlagsService.startLoggedClient(customerId)).pipe(
      switchMap(() => this.setLoggedAttributes(customerId)),
      switchMap(() => this._featureFlagsService.getLoggedFeature$(SPLITS.SPLIT_METHOD_PAYMENTS).pipe(take(1)))
    );
  }

  private setLoggedAttributes(customerId: string): Observable<any> {
    const hourOfDay = +new Date().toLocaleTimeString('es', { timeZone: 'America/Mexico_City' }).split(':')[0];
    return from(
      this._featureFlagsService.setLoggedAttributes({
        customerId: String(customerId),
        operatingSystem: 'web',
        merchantsId: this.checkoutInfo.merchant_id,
        sourceType: this.checkoutInfo.source_type,
        isNextQuincena: this.checkoutInfo.next_quincena_enabled,
        isPayNow: this.checkoutInfo.customer_type === 'PAY_NOW',
        customerType: this.checkoutInfo.customer_type,
        numberOfInstallments: this.schemeSelected.installments,
        hourOfDay,
      })
    );
  }

  updateCheckoutInfoAndScheme(): void {
    this._checkoutInfoFacade.checkoutInfo$
      .pipe(
        withLatestFrom(this._schemeFacade.getSchemeSelected$, this._planConfirmationFacade.useAplazoPoints$),
        map(([checkoutInfo, schemeSelected, useAplazoPoints]) => {
          this.useAplazoPoints = useAplazoPoints;
          this.checkoutInfo = checkoutInfo;
          this.isVirtualCard = this.checkoutInfo?.source_type === 'VC';

          const queryInstallments = this.getQueryInstallments();

          if (queryInstallments && this.checkoutInfo?.schemes) {
            const matchedScheme = this.getSchemeByInstallments(this.checkoutInfo.schemes, queryInstallments);

            if (matchedScheme) {
              this.schemeSelected = matchedScheme;
              this._schemeFacade.setInfoCheckout(this.schemeSelected);
            } else {
              this.schemeSelected = this.checkoutInfo.schemes[0];
              this._schemeFacade.setInfoCheckout(this.schemeSelected);
            }
          } else if (schemeSelected && this.checkoutInfo?.schemes) {
            this.schemeSelected = schemeSelected;
            this._schemeFacade.setInfoCheckout(this.schemeSelected);
          } else if (this.checkoutInfo?.schemes) {
            this.schemeSelected = this.checkoutInfo.schemes[0];
            this._schemeFacade.setInfoCheckout(this.schemeSelected);
          }
          this.canPayNextQuincena = this.checkoutInfo?.next_quincena_enabled;
          this.promoCodesEnabled = this.checkoutInfo?.promo_code_enabled;
          this.showButtonChangeInstallment = this.checkoutInfo?.schemes.length > 1;
          this.paymentType = this.canPayNextQuincena
            ? PaymentTypes.firstInstallmentNextFornight
            : PaymentTypes.firstInstallment;

          this.isDisabledSubmit =
            this.checkoutInfo?.purchase_amount -
              this.schemeSelected?.customer_discounts_applied?.reduce((t, discount) => t + discount.amount, 0) <
            0;
          this.setDiscounts();
          this.isDynamicInstallments = this.schemeSelected?.id !== -1;
          return [];
        }),
        map(() => {
          this._featureFlagsService
            .getAnonymousFeature$(SPLITS.SPLIT_CHECKOUT_APLAZO_POINTS)
            .pipe(
              take(1),
              map(featureFlag => !!featureFlag.value)
            )
            .subscribe(ff => {
              this.showCheckAplazoPoints = ff && this.schemeSelected?.aplazo_points_amounts?.aplazo_points_used > 0;
              this.aplazoPointsUsed = this.schemeSelected?.aplazo_points_amounts?.aplazo_points_used;
              this.useAplazoPoints = this.useAplazoPoints ?? false; // if useAplazoPoints is null, set it to false or change for showCheckAplazoPoints to force check to true at the beginning
              this._planConfirmationFacade.setUseAplazoPoints(this.useAplazoPoints);
              this.forceUseAplazoPoints =
                (this.schemeSelected?.fee_amount === null || this.schemeSelected?.fee_amount === 0) &&
                this.schemeSelected.aplazo_points_amounts?.fee_amount >= 0;
              if (this.forceUseAplazoPoints) {
                this.useAplazoPoints = this.forceUseAplazoPoints;
              }

              this.setAmounts();
            });
        }),
        switchMap(() => {
          return this._translocoService.selectTranslateObject(this._getFlowTranslate(this.checkoutInfo?.source_type));
        }),
        takeUntil(this._unsubscribe$)
      )
      .subscribe(textTemplate => {
        if (this.checkoutInfo === null || this.schemeSelected === null) {
          this._pagesFacade.setCurrentPage(Pages.default);
          return;
        }
        this.txtTemplate = textTemplate;
        this.setMethodPaymentsAvailable();
      });
  }

  useAplazoPointsChange(value): void {
    this.useAplazoPoints = value;
    this._planConfirmationFacade.setUseAplazoPoints(this.useAplazoPoints);
    this.setAmounts();
  }

  setAmounts(): void {
    if (this.schemeSelected) {
      const currentDate = new Date().toDateString();
      this.calendar = this._calendarService.getCalendar(
        { baseDate: currentDate, calendar: this.schemeSelected?.calendar },
        this.useAplazoPoints ? this.schemeSelected?.aplazo_points_amounts?.fee_amount : this.schemeSelected?.fee_amount,
        this.useAplazoPoints
          ? this.schemeSelected?.aplazo_points_amounts?.first_installment_extra_amount
          : this.schemeSelected?.first_installment_extra_amount,
        this.checkoutInfo.customer_type === 'BNPL' ? 'Paga hoy' : 'Paga ahora',
        this.useAplazoPoints
          ? this.schemeSelected?.aplazo_points_amounts?.total_first_installment
          : this.schemeSelected?.total_first_installment,
        this.schemeSelected?.first_installment > 0
          ? this.schemeSelected?.first_installment
          : this.schemeSelected?.fee_amount
      );
    }
  }

  private _subscribePayment(): void {
    this._paymentService.payLoan$.pipe(takeUntil(this._unsubscribe$)).subscribe();
  }

  public setDiscounts() {
    this.discounts =
      this.schemeSelected?.customer_discounts_applied?.map(discount => {
        return {
          text: discount.name,
          amount: discount.amount * -1,
          description: discount.description,
          isFromDiscountCode: discount.fromDiscountCode,
          requiredInstallments: discount.requiredInstallments,
          code: discount.code,
          showDescription: !!discount.description && !discount.fromDiscountCode,
        } as IDiscount;
      }) ?? [];
  }

  public goToSelectPlan(): void {
    this._pagesFacade.setCurrentPage(Pages.selectPlan);
  }

  public updateCheckoutInfo(cardAdded: boolean): void {
    if (cardAdded) {
      this._uiFacade.setLoading(true);
      this._checkoutInfoFacade.refreshCheckout$();
    }
  }

  private _getFlowTranslate(integrationType: string = null): string {
    const defaultTranslationKey = translationKey.get('STORES');
    const currentKey = translationKey.get(integrationType) || defaultTranslationKey;
    return `${currentKey}.${this.paymentType}`;
  }

  discountAdded(): void {
    this.enableAddPromoCode = false;
    this._checkoutInfoFacade.refreshCheckout$();
  }

  validateDiscountCode = (discountCode: string): Observable<string | null> => {
    this._uiFacade.setLoading(true);
    const code = String(discountCode);
    const loanId = String(this.checkoutInfo?.id);
    return this._discountsService
      .validatePromoCode(code, this.schemeSelected?.installments, loanId)
      .pipe(finalize(() => this._uiFacade.setLoading(false)));
  };

  discountRemoved(discountRemoved: IDiscount): void {
    this.discardDiscountCode(discountRemoved.text)
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe(() => (this.enableAddPromoCode = true));
  }

  private discardDiscountCode = (discountCodeRemoved: string): Observable<void> => {
    this._uiFacade.setLoading(true);
    const loanId = String(this.checkoutInfo?.id);
    return this._discountsService.discardPromoCode(discountCodeRemoved, this.schemeSelected?.installments, loanId).pipe(
      tap(() => {
        this._checkoutInfoFacade.refreshCheckout$();
      }),
      finalize(() => this._uiFacade.setLoading(false))
    );
  };

  private _setPaymentButtonFixedForMobile(): void {
    this.paymentButtonFixed =
      window.innerWidth >= this._minWidthButtonFixed && window.innerWidth <= this._maxWidthButtonFixed;
  }

  private codiErrorHandler(): void {
    this._codiService
      .getCodiError$()
      .pipe(
        takeUntil(this._unsubscribe$),
        withLatestFrom(this._translocoService.selectTranslateObject('OTHER_PAYMENT_METHODS.CODI'))
      )
      .subscribe(([res, txtTemplate]) => {
        if (res.error.code === '4609') {
          this._uiFacade.displayNotification(
            ITypeNotification.error,
            'Se produjo un error inesperado. Inténtelo de nuevo.'
          );
        }
        if (res.error.code === '4601') {
          this.openCodiErrorModal(txtTemplate.ERROR_4601, res.error);
        }
        if (res.error.code === '4603') {
          this.openCodiErrorModal(txtTemplate.ERROR_4603, res.error);
        }
        this._ngZone.run(() => {
          this._uiFacade.setLoading(false);
        });
      });
  }

  private openCodiErrorModal(data: any, error): void {
    this._errorsService
      .getErrorCode(error.code, this.checkoutInfo.id)
      .pipe(take(1))
      .subscribe(code => {
        this._dialog.open(ModalCodiErrorComponent, {
          disableClose: false,
          maxWidth: '400px',
          width: '100%',
          data: {
            ...data,
            errorCode: code,
          },
        });
      });
  }

  /**
   * Extracts and validates the installments parameter from URL query parameters
   * @returns The validated installments number or null if invalid/missing
   */
  private getQueryInstallments(): number | null {
    try {
      const params = this._route.snapshot.queryParams;
      const installmentsParam = params['installments'];

      if (installmentsParam) {
        const installments = parseInt(installmentsParam, 10);
        if (!isNaN(installments) && installments > 0) {
          return installments;
        }
      }

      if (this._storedCashiParams.installments) {
        return this._storedCashiParams.installments;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Extracts the offerId parameter from URL query parameters
   * @returns The offerId string or null if not present
   */
  private getQueryOfferId(): string | null {
    try {
      const params = this._route.snapshot.queryParams;
      const offerId = params['offerId'];

      if (offerId && typeof offerId === 'string' && offerId.trim().length > 0) {
        return offerId.trim();
      }

      if (this._storedCashiParams.offerId) {
        return this._storedCashiParams.offerId;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Finds a scheme by the specified number of installments
   * @param schemes Array of available schemes
   * @param installments Number of installments to match
   * @returns The matching scheme or null if not found
   */
  private getSchemeByInstallments(schemes: IScheme[], installments: number): IScheme | null {
    try {
      if (!Array.isArray(schemes) || schemes.length === 0) {
        return null;
      }

      const matchingSchemes = schemes.filter(scheme => scheme.installments === installments);

      if (matchingSchemes.length === 0) {
        return null;
      }

      if (matchingSchemes.length === 1) {
        return matchingSchemes[0];
      }

      const selectedScheme = matchingSchemes.reduce((prev, current) => (prev.id < current.id ? prev : current));
      return selectedScheme;
    } catch (error) {
      return null;
    }
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
