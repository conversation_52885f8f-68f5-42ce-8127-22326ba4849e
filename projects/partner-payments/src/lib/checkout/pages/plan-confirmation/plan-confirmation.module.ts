import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PlanConfirmationRoutingModule } from './plan-confirmation-routing.module';
import { PlanConfirmationComponent } from './plan-confirmation.component';
import { WebUiModule } from '@aplazo/web-ui';
import { PaymentMethodsModule } from '../../../payment-methods';
import { ComponentsModule } from '../../components/components.module';

@NgModule({
  declarations: [PlanConfirmationComponent],
  imports: [CommonModule, PlanConfirmationRoutingModule, WebUiModule, ComponentsModule, PaymentMethodsModule],
})
export class PlanConfirmationModule {}
