import { DOCUMENT } from '@angular/common';
import { Component, Inject, <PERSON><PERSON><PERSON>, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { DatalayerService } from '@aplazo/partner-analytics';
import { UiCoreFacade } from '@aplazo/partner-core';
import { TranslocoService } from '@ngneat/transloco';
import { Subject, combineLatest, filter, takeUntil } from 'rxjs';
import { DataLayerEventName, DataLayerEventTypes } from '../../enums/datalayer-events.enum';
import { Pages } from '../../enums/pages.enum';
import { ErrorsHandlerService } from '../../services/errors-handler/errors-handler.service';
import { PagesFacade } from '../../store/facades/page.facede';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { SchemeFacade } from '../../store/facades/scheme.facade';
import { IScheme } from '../../interfaces/checkout-info.interface';

@Component({
  selector: 'aplazo-payments-select-installment',
  templateUrl: './select-installment.component.html',
  styleUrls: ['./select-installment.component.scss'],
})
export class SelectInstallmentComponent implements OnInit, OnDestroy {
  public formGroup: UntypedFormGroup;
  public txtTemplate: Record<string, string>;
  public schemes: IScheme[];
  public schemeSelected: IScheme;
  public isPayNextQuincenaActive: boolean = false;
  private _defaultInstallments = 5;
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();
  private SCHEME_SELECTED = 'schemeSelected';

  constructor(
    @Inject(DOCUMENT) public readonly _document: Document,
    private _uiFacade: UiCoreFacade,
    private _pagesFacade: PagesFacade,
    private _fb: UntypedFormBuilder,
    private _translocoService: TranslocoService,
    private _errorsHandlerService: ErrorsHandlerService,
    private _datalayerScv: DatalayerService,
    private _checkoutInfoFacade: CheckoutInfoFacade,
    private _schemeFacade: SchemeFacade,
    private _ngZone: NgZone
  ) {
    this._uiFacade.setLoading(true);
    this.formGroup = this._fb.group({
      schemeSelected: [null, [Validators.required]],
    });
  }

  public ngOnInit(): void {
    this._datalayerScv.datalayerEvent(DataLayerEventName.SELECT_SCHEMA, DataLayerEventTypes.PAGEVIEW, this._document);
    combineLatest({
      checkoutInfo: this._checkoutInfoFacade.checkoutInfo$.pipe(filter(info => !!info)),
      scheme: this._schemeFacade.getSchemeSelected$,
      translate: this._translocoService.selectTranslateObject('PAYMENTS_MODULE.SELECT_INSTALLMENTS'),
    })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        next: data => {
          this.isPayNextQuincenaActive = data.checkoutInfo.next_quincena_enabled;
          this.schemes = data.checkoutInfo.schemes;
          this._setDefaultScheme();
          this.txtTemplate = data.translate;
          this._ngZone.run(() => {
            this._uiFacade.setLoading(false);
            this._uiFacade.setShowButtonCancel(false);
          });
        },
        error: error => {
          this._errorsHandlerService.errorHandler(error);
        },
      });
  }

  private _setDefaultScheme(): void {
    this.schemeSelected =
      this.schemes.find(({ installments }) => this._defaultInstallments === installments) ?? this.schemes[0];
    this.formGroup.get(this.SCHEME_SELECTED).setValue(this.schemeSelected);
  }

  public selectScheme(): void {
    this._schemeFacade.setInfoCheckout(this.formGroup.get(this.SCHEME_SELECTED).value);
    this._pagesFacade.setCurrentPage(Pages.planConfirmation);
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
