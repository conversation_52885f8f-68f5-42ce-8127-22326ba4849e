import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SelectInstallmentRoutingModule } from './select-installment-routing.module';
import { SelectInstallmentComponent } from './select-installment.component';
import { WebUiModule } from '@aplazo/web-ui';
import { ComponentsModule } from '../../components/components.module';


@NgModule({
  declarations: [
    SelectInstallmentComponent
  ],
  imports: [
    CommonModule,
    SelectInstallmentRoutingModule,
    WebUiModule,
    ComponentsModule
  ]
})
export class SelectInstallmentModule { }
