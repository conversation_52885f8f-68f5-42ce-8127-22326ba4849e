<aplazo-message-template
  *ngIf="txtTemplate"
  [riveUrl]="txtTemplate.riveUrl"
  [txtTemplate]="txtTemplate"
  [showContent]="true"
  (btnSelected)="goToLandingpage()"
>
  <div content>
    <div class="aplazo-points-used" *ngIf="useAplazoPoints">
      <img class="animation" [src]="txtTemplate.aplazoPointsUsed.image" alt="" />
      <div class="text-content">
        <p class="title">
          {{ txtTemplate.aplazoPointsUsed.titleFirst }}
          {{ aplazoPointsUsed }}
          {{ txtTemplate.aplazoPointsUsed.titleSecond }}
        </p>
        <p class="subtitle">{{ txtTemplate.aplazoPointsUsed.subtitle }}</p>
      </div>
    </div>
    <aplazo-payments-earned-aplazo-points
      *ngIf="showPromoAplazoPoints"
      [earnedAplazoPoints]="promoCashback"
      [textTemplate]="txtTemplate.earnedAplazoPoints"
    ></aplazo-payments-earned-aplazo-points>
  </div>
</aplazo-message-template>
