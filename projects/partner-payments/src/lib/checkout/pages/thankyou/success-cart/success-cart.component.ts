import { DOCUMENT } from '@angular/common';
import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { DatalayerService } from '@aplazo/partner-analytics';
import {
  PartnerCoreEnvironment,
  PARTNER_CORE_ENVIRONMENT,
  UiCoreFacade,
  CustomerFacade,
  IMe,
} from '@aplazo/partner-core';
import { TranslocoService } from '@ngneat/transloco';
import { combineLatest, from, Observable, Subject, switchMap, take, takeUntil } from 'rxjs';
import { DataLayerEventName, DataLayerEventTypes } from '../../../enums/datalayer-events.enum';
import { _checkoutInfoReducer } from '../../../store/reducers/checkout-info.reducer';
import { CheckoutInfoFacade } from '../../../store/facades/checkout-info.facade';
import { FeatureFlagsService } from '@aplazo/front-feature-flags';
import { ICheckoutInfo, IScheme } from '../../../interfaces/checkout-info.interface';
import { SPLITS } from '../../../../payment-methods/enums/splits.enum';
import { SchemeFacade } from '../../../store/facades/scheme.facade';
import { PlanConfirmationFacade } from '../../../store/facades/plan-confirmation.facade';

@Component({
  selector: 'aplazo-payments-success-cart',
  templateUrl: './success-cart.component.html',
  styleUrls: ['./success-cart.component.scss'],
})
export class SuccessCartComponent implements OnInit, OnDestroy {
  public txtTemplate: Record<string, any>;
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();
  private checkoutInfo: ICheckoutInfo;
  private schemeSelected: IScheme;
  private me: IMe;

  public showPromoAplazoPoints: boolean = false;
  public promoCashback: string = null;
  public aplazoPointsUsed: number = 0;
  public useAplazoPoints: boolean = false;

  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    @Inject(DOCUMENT) public readonly _document: Document,
    private _translocoService: TranslocoService,
    private _uiFacade: UiCoreFacade,
    private _datalayerScv: DatalayerService,
    private _checkoutInfoFacade: CheckoutInfoFacade,
    private _schemeSelectedFacade: SchemeFacade,
    private _featureFlagService: FeatureFlagsService,
    private _customerFacade: CustomerFacade,
    private _planConfirmationFacade: PlanConfirmationFacade
  ) {
    this._uiFacade.setLoading(true);
  }

  public ngOnInit(): void {
    this._subscribeGetSuccesCart();
    this._datalayerScv.datalayerEvent(
      DataLayerEventName.PLAN_CONFIRMATION,
      DataLayerEventTypes.PAGEVIEW,
      this._document
    );
  }

  public goToLandingpage(): void {
    window.location.href = `${this._environment.aplazo.landingpage}`;
  }

  private _subscribeGetSuccesCart(): void {
    combineLatest({
      checkoutInfo: this._checkoutInfoFacade.checkoutInfo$,
      schemeSelected: this._schemeSelectedFacade.getSchemeSelected$,
      useAplazoPoints: this._planConfirmationFacade.useAplazoPoints$,
      txtTemplate: this._translocoService.selectTranslateObject('PAYMENTS_MODULE.SUCCESS_PAYMENT.V2'),
      me: this._customerFacade.me$.pipe(take(1)),
    })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe(data => {
        this.checkoutInfo = data.checkoutInfo;
        this.me = data.me;
        this.schemeSelected = data.schemeSelected;
        this.txtTemplate = data.txtTemplate;
        this.useAplazoPoints = data.useAplazoPoints;
        this.aplazoPointsUsed = this.schemeSelected?.aplazo_points_amounts?.aplazo_points_used;
        this.processLoggedFeatureFlag(SPLITS.SPLIT_CASHBACK);
      });
  }

  private processLoggedFeatureFlag(featureFlag: string) {
    this._featureFlagService.refresh();
    from(this._featureFlagService.startLoggedClient(this.me.id.toString()))
      .pipe(
        switchMap(() => this.setLoggedAttributes()),
        switchMap(() => this._featureFlagService.getLoggedFeature$(featureFlag).pipe(take(1)))
      )
      .subscribe(featureFlagValue => {
        if (featureFlagValue.value) {
          this.promoCashback = featureFlagValue.value.toString();
          this.showPromoAplazoPoints = true;
        }
        this._uiFacade.setLoading(false);
        this._uiFacade.setShowButtonCancel(false);
      });
  }

  private setLoggedAttributes(): Observable<any> {
    const hourOfDay = +new Date().toLocaleTimeString('es', { timeZone: 'America/Mexico_City' }).split(':')[0];
    return from(
      this._featureFlagService.setLoggedAttributes({
        customerId: String(this.me.id),
        operatingSystem: 'web',
        merchantsId: this.checkoutInfo.merchant_id,
        sourceType: this.checkoutInfo.source_type,
        isNextQuincena: this.checkoutInfo.next_quincena_enabled,
        customerType: this.checkoutInfo.customer_type,
        isPayNow: this.checkoutInfo.customer_type === 'PAY_NOW',
        hourOfDay,
      })
    );
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
