import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SuccessCartRoutingModule } from './success-cart-routing.module';
import { SuccessCartComponent } from './success-cart.component';
import { ComponentsModule } from './../../../components/components.module';
import { WebUiModule } from '@aplazo/web-ui';

@NgModule({
  declarations: [SuccessCartComponent],
  imports: [CommonModule, WebUiModule, SuccessCartRoutingModule, ComponentsModule],
})
export class SuccessCartModule {}
