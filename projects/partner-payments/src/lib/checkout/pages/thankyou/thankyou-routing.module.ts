import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ThankyouPages } from '../../enums/pages.enum';

const routes: Routes = [
  {
    path: ThankyouPages.FIRST_INSTALLMENT_NEXT_FORNIGHT,
    loadChildren: () => import('./success-cart/success-cart.module').then(m => m.SuccessCartModule),
  },
  {
    path: ThankyouPages.FIRST_INSTALLMENT,
    loadChildren: () => import('./success-cart/success-cart.module').then(m => m.SuccessCartModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ThankyouRoutingModule {}
