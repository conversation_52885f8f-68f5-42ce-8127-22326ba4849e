import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { PARTNER_CORE_ENVIRONMENT, PartnerCoreEnvironment } from '@aplazo/partner-core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AccountValidationService {
  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _http: HttpClient
  ) {}

  public dobVerification(date: string): Observable<any> {
    return this._http.post(`${this._environment.api.mobileInquiry}api/inquiry/v1/customer-info/dob-verification`, {
      dateOfBirth: date,
    });
  }
}
