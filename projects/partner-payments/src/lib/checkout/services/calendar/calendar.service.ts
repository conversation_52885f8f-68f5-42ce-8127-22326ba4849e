import { Injectable } from '@angular/core';
import { format } from 'date-fns';
import { es } from 'date-fns/esm/locale';
import { IStep } from '@aplazo/web-ui';
import { CurrencyPipe } from '@angular/common';
import { ICalendar } from '@aplazo/partner-core';

@Injectable({
  providedIn: 'root',
})
export class CalendarService {
  constructor(private _currencyPipe: CurrencyPipe) {}

  public getCalendar(
    { calendar, baseDate }: ICalendar,
    feeAmount: number,
    firstInstallmentExtraAmount: number,
    firstPaymentText: string,
    totalFirstInstallment: number,
    firstInstallment: number
  ): IStep[] {
    return calendar.map((date, index) => {
      const isFirstInstallment = index === 0;
      const amount = isFirstInstallment ? totalFirstInstallment : feeAmount;
      const compareDate = new Date(date).toDateString();
      const formattedDate = format(new Date(date), "dd 'de' MMM", { locale: es });
      return {
        status: isFirstInstallment ? 'active' : '',
        title: [compareDate === baseDate ? firstPaymentText : formattedDate, `${this._currencyPipe.transform(amount)}`],
        details:
          isFirstInstallment && firstInstallmentExtraAmount
            ? this._getCalendarDetails(firstInstallment, firstInstallmentExtraAmount)
            : {},
      };
    });
  }

  private _getCalendarDetails(
    feeAmount: number,
    firstInstallmentExtraAmount: number
  ): {
    titleExtraAmount: string;
    extraAmount: string;
    titleFirstPay: string;
    firstPay: string;
  } {
    return {
      titleExtraAmount: 'Tu enganche será de:',
      extraAmount: `${this._currencyPipe.transform(firstInstallmentExtraAmount)}`,
      titleFirstPay: 'Tu primer cuota',
      firstPay: `${this._currencyPipe.transform(feeAmount)}`,
    };
  }
}
