import { HttpClient, HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { PartnerCoreEnvironment, PARTNER_CORE_ENVIRONMENT } from '@aplazo/partner-core';
import { IDiscount } from '@aplazo/web-ui';
import { catchError, map, mergeMap, Observable, of, throwError } from 'rxjs';
import { PlanConfirmationFacade } from '../../store/facades/plan-confirmation.facade';

@Injectable({
  providedIn: 'root',
})
export class DiscountsService {
  constructor(
    private _http: HttpClient,
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment
  ) {}

  validatePromoCode(discountCode: string, installments: number, loanId: string): Observable<string | null> {
    return this._http
      .post<void>(`${this._environment.api.baseUrl}customer/loans/${loanId}/discounts/validate-code`, {
        code: discountCode,
        numberOfInstallments: installments,
      })
      .pipe(
        map(() => discountCode),
        catchError((error: HttpErrorResponse) => {
          if (error.status === HttpStatusCode.BadRequest) {
            return throwError(() => new Error(`[${error.error.code}] ${error.error.message}`)); //! TODO: Reemplazar por errores personalizados
          } else {
            return of(null);
          }
        })
      );
  }

  discardPromoCode(discountCode: string, installments: number, loanId: string): Observable<void> {
    return this._http
      .post<void>(`${this._environment.api.baseUrl}customer/loans/${loanId}/discounts/discard-code`, {
        code: discountCode,
        numberOfInstallments: installments,
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          return throwError(() => new Error(`[${error.error.code}] ${error.error.message}`));
        })
      );
  }
}
