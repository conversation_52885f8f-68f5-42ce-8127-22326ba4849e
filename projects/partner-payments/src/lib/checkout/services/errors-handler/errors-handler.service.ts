import { Injectable } from '@angular/core';
import { PaymentFacade } from '../../../payment-methods/store/facades/payment.facade';
import { ErrorCodes, ErrorPages, Pages } from '../../enums/pages.enum';
import { IError } from '../../interfaces/error.interface';
import { PagesFacade } from '../../store/facades/page.facede';

@Injectable({
  providedIn: 'root',
})
export class ErrorsHandlerService {
  constructor(private _pagesFacade: PagesFacade, private _paymentFacade: PaymentFacade) { }

  public errorHandler({ status, error }: IError): void {
    const code: string = error.code ?? error.error ?? error.status ?? undefined;
    const errorStatus = [406, 417];
    const errorCode = errorStatus.includes(status) ? status : code;
    this._getErrorByCode(String(errorCode), status);
  }

  private _getErrorByCode(code: string, status: number): void {
    switch (code) {
      case ErrorCodes.PURCHASE_LIMIT_PER_DAY:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.PURCHASE_LIMIT_PER_DAY);
      case ErrorCodes.NOT_CREDIT_AVAILABLE:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.NOT_CREDIT_AVAILABLE);
      case ErrorCodes.BANK:
        this._paymentFacade.setCardNeedsToBeReplaced(true);
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.BANK);
      case ErrorCodes.KYC:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.KYC);
      case ErrorCodes.MERCHANT_CONFIGURATION:
      case ErrorCodes.BRANCH_CONFIGURATION:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.MERCHANT_CONFIGURATION);
      case ErrorCodes.LOAN_REJECTED_NRT:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_NRT);
      case ErrorCodes.LOAN_REJECTED_HARD_RULE:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_HARD_RULE);
      case ErrorCodes.LOAN_ON_HOLD:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_ON_HOLD);
      case ErrorCodes.LOAN_REJECTED_KOUNT_OFFLINE:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_KOUNT_OFFLINE);
      case ErrorCodes.LOAN_REJECTED_NRT_OFFLINE:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_NRT_OFFLINE);
      case ErrorCodes.LOAN_REJECTED_BOTH:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_BOTH);
      case ErrorCodes.LOAN_REJECTED_KOUNT_APPROVE_NRT:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_KOUNT_APPROVE_NRT);
      case ErrorCodes.LOAN_REJECTED_NRT_APPROVE_KOUNT:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_NRT_APPROVE_KOUNT);
      case ErrorCodes.LOAN_REJECTED_BOTH_ONLINE:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_REJECTED_BOTH_ONLINE);
      case ErrorCodes.LATE_PAYMENTS:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LATE_PAYMENTS);
      case ErrorCodes.CARD_NO_FUNDS_FIRST_TIME:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.CARD_NO_FUNDS_FIRST_TIME);
      case ErrorCodes.CARD_NO_FUNDS_SECOND_TIME:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.CARD_NO_FUNDS_SECOND_TIME);
      case ErrorCodes.LOAN_CANCELLED:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_CANCELLED);
      case ErrorCodes.PURCHASE_LIMIT:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.PURCHASE_LIMIT);
      case ErrorCodes.INVALID_DISCOUNT_CODE:
      case ErrorCodes.INVALID_DISCOUNT_CODE_DISABLED:
      case ErrorCodes.INVALID_DISCOUNT_CODE_EXPIRED:
      case ErrorCodes.INVALID_DISCOUNT_CODE_NOT_ACTIVE:
      case ErrorCodes.INVALID_DISCOUNT_CODE_NOT_IN_CACHE:
      case ErrorCodes.INVALID_DISCOUNT_CODE_COUPON_ALREADY_APPLIED:
      case ErrorCodes.INVALID_DISCOUNT_CODE_EXCEDED_THE_MAXIMUM_ALLOWED:
      case ErrorCodes.INVALID_DISCOUNT_CODE_EXCEEDS_LOAN:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.INVALID_DISCOUNT_CODE);
      case ErrorCodes.DISCOUNT_SERVICE_ERROR:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.DISCOUNT_SERVICE_ERROR);
      case ErrorCodes.DISCOUNT_SERVICE_EXCEPTION:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.DISCOUNT_SERVICE_EXCEPTION);
      case ErrorCodes.DISCOUNT_REDEMPTION_ERROR:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.DISCOUNT_REDEMPTION_ERROR);
      case ErrorCodes.REJECTED_KOUNT_VC:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.REJECTED_KOUNT_VC);
      case ErrorCodes.REJECTED_NRT_VC:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.REJECTED_NRT_VC);
      case ErrorCodes.REJECTED_BOTH_VC:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.REJECTED_BOTH_VC);
      case ErrorCodes.LOAN_DIFF_TO_REQUEST:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.LOAN_DIFF_TO_REQUEST);
      case ErrorCodes.KYC_FALSE:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.KYC_FALSE);
      case ErrorCodes.EXTRA_CREDIT_EXCEEDED:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.EXTRA_CREDIT_EXCEEDED);
      case ErrorCodes.CAN_NOT_ADD_CARD:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.CAN_NOT_ADD_CARD);
      case ErrorCodes.CAN_NOT_ADD_CARD_BY_CONNECTION:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.CAN_NOT_ADD_CARD_BY_CONNECTION);
      case ErrorCodes.ERROR_409:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.PAYMENT_ERROR_409);
      case ErrorCodes.INVALID_LINK:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.INVALID_LINK);
      default:
        return this._pagesFacade.setCurrentPage(Pages.error, ErrorPages.SERVER, { code: status });
    }
  }
}
