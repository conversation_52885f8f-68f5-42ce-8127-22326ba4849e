import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import {
  AppSections,
  AppStateFacade,
  IAppState,
  LoginCoreFacade,
  PartnerCoreEnvironment,
  PARTNER_CORE_ENVIRONMENT,
  CustomerFacade,
  LoanFacade,
} from '@aplazo/partner-core';
import { filter, map, Observable, take } from 'rxjs';
import { Pages } from '../../enums/pages.enum';
import { PagesFacade } from '../../store/facades/page.facede';
import { LoanService } from '../loan/loan.service';
import { PaymentCheckoutService } from '../payment-checkout/payment-checkout.service';

@Injectable({
  providedIn: 'root',
})
export class ErrorsService {
  constructor(
    @Inject(DOCUMENT) private readonly _document: Document,
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _loginFacade: LoginCoreFacade,
    private _loanService: LoanService,
    private _pagesFacade: PagesFacade,
    private _paymentService: PaymentCheckoutService,
    private _appStateFacade: AppStateFacade,
    private _customerFacade: CustomerFacade,
    private _loanFacade: LoanFacade,

  ) {}

  public payLoan$ = this._paymentService.payLoan$; // TODO analyze if this is necessary

  public paymentReset(): void {
    this._paymentService.reset();
    this._pagesFacade.setCurrentPage(Pages.planConfirmation);
  }

  public goToOtherStores(): void {
    this._document.location.href = `${this._environment.aplazo.landingpage}tiendas/Todas`;
  }

  public openContactSupport(message: string): Window {
    return window.open(`${this._environment.aplazo.whatsapp}&text=${message}`, '_blank');
  }

  private _cleanStore(): void {
    this._loginFacade.resetAuthToken();
    this._loanService.reset();
    this._customerFacade.resetCustomer();
    this._appStateFacade.resetError();
  }

  public logOut(): void {
    this._cleanStore();
    this._pagesFacade.goLoginPage();
  }

  public goToDashboard(): void {
    this._cleanStore();
    this._document.location.href = this._environment.aplazo.customerLoginDashboard;
  }

  public returnToStore(): void {
    this._loanFacade.loan$.pipe(take(1)).subscribe(loan => {
      this._cleanStore();
      this._document.location.href = loan.cartUrl;
    });
  }

  public getErrorCode(code: string, loanId?: number): Observable<string> {
    this._appStateFacade.setError(code, AppSections.PAYMENTS);
    return this._appStateFacade.appState$.pipe(
      filter(({ error }) => !!error),
      map(({ name, version, error: { code } }: IAppState) => {
        const loan = loanId ? `-${loanId}-` : '';
        return `${name}${loan}${code || ''}-${version}`;
      })
    );
  }
}
