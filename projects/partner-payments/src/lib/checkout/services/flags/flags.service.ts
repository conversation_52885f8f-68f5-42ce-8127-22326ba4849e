import { Injectable } from '@angular/core';
import { FlagFacade } from '@aplazo/partner-core';
import { combineLatest, filter, first, map, Observable } from 'rxjs';
import { FeatureFlag } from '../../enums/flags.enum';

@Injectable({
  providedIn: 'root',
})
export class FlagsService {
  private _withBranchFlags: boolean;

  private _withCustomerFlags: boolean;

  constructor(private _flagsFacade: FlagFacade) {
    this._withBranchFlags = false;
    this._withCustomerFlags = false;
  }

  public setFlagsMerchant(merchantId: number): void {
    this._flagsFacade.getFlagsMerchant(merchantId);
  }

  public setFlagsBranch(branchId: string): void {
    this._withBranchFlags = !!branchId;
    this._flagsFacade.getFlagsMerchantBranch(branchId);
  }

  public getFeatureFlags$(): Observable<string[]> {
    const flags = [this._getFlagsByMerchantId$()];

    if (this._withCustomerFlags) {
      flags.push(this._getFlagsByCustomerId$());
    }

    if (this._withBranchFlags) {
      flags.push(this._getFlagsByBranchId$());
    }

    return combineLatest(flags).pipe(
      map(featureFlags => this.evaluationFlags(featureFlags)),
      first()
    );
  }

  private _getFlagsByMerchantId$(): Observable<string[]> {
    return this._flagsFacade.flagsMerchant$.pipe(filter(flags => !!flags));
  }

  private _getFlagsByCustomerId$(): Observable<string[]> {
    return this._flagsFacade.flagsCustomer$.pipe(filter(flags => !!flags));
  }

  private _getFlagsByBranchId$(): Observable<string[]> {
    return this._flagsFacade.flagsMerchantBranch$.pipe(filter(flags => !!flags));
  }

  public setCustomerFlags(getFlags: boolean): void {
    this._withCustomerFlags = getFlags;
  }

  public evaluationFlags([flagsMerchant, flagsCustomer, flagsMerchantBranch]: string[][]): string[] {
    let getMerchantAndBranchFlags = [...new Set([...flagsMerchant, ...(flagsMerchantBranch || [])])];
    return getMerchantAndBranchFlags.filter(flag => {
      if (flag === FeatureFlag.payNextQuincena) return flagsCustomer?.includes(FeatureFlag.payNextQuincena);
      return !!flag;
    });
  }
}
