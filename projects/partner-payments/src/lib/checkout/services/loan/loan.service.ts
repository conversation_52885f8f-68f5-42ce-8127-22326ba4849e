import { Inject, Injectable } from '@angular/core';
import { LoanFacade, PARTNER_CORE_ENVIRONMENT, PartnerCoreEnvironment } from '@aplazo/partner-core';
import { catchError, Observable } from 'rxjs';
import { ICheckoutInfo } from '../../interfaces/checkout-info.interface';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class LoanService {
  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _http: HttpClient,
    private _loanFacade: LoanFacade
  ) {}

  public reset(): void {
    this._loanFacade.resetLoan();
  }

  public cleanLoanToken(): void {
    this._loanFacade.clearToken();
  }

  public getCheckoutInfoById(id: string): Observable<ICheckoutInfo> {
    const headers = new HttpHeaders().set('platform', 'web');
    return this._http.get<ICheckoutInfo>(`${this._environment.api.msPayments}api/v1/checkout/${id}`, { headers }).pipe(
      catchError(error => {
        throw error;
      })
    );
  }
}
