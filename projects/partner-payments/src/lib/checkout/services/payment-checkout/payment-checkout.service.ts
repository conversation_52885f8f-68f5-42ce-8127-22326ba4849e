import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { VirtualCardFacade } from '@aplazo/client-virtual-card';
import { PluginIntegrations } from '@aplazo/partner-core';
import { tap, withLatestFrom } from 'rxjs';
import { IPaymentResponsePayload } from '../../../payment-methods/interfaces/payment.interface';
import { PaymentFacade } from '../../../payment-methods/store/facades/payment.facade';
import { ErrorCodes, OtherPages, OtherPaymentMethods, Pages, ThankyouPages } from '../../enums/pages.enum';
import { PagesFacade } from '../../store/facades/page.facede';
import { ErrorsHandlerService } from '../errors-handler/errors-handler.service';
import { CheckoutInfoFacade } from '../../store/facades/checkout-info.facade';
import { PlanConfirmationFacade } from '../../store/facades/plan-confirmation.facade';

@Injectable({
  providedIn: 'root',
})
export class PaymentCheckoutService {
  constructor(
    private _virtualCardFacade: VirtualCardFacade,
    private _paymentFacade: PaymentFacade,
    private _pagesFacade: PagesFacade,
    private _router: Router,
    private _errorsHandlerService: ErrorsHandlerService,
    private _checkoutInfoFacade: CheckoutInfoFacade,
    private _planConfirmationFacade: PlanConfirmationFacade
  ) {}

  public payLoan$ = this._paymentFacade.payLoan$.pipe(
    withLatestFrom(this._checkoutInfoFacade.checkoutInfo$, this._planConfirmationFacade.usePaymentCodi$),
    tap(([paymentResponse, checkoutInfo, useCodi]) => {
      const amount = checkoutInfo.purchase_amount;
      const loanId = checkoutInfo.id;
      const merchantId = checkoutInfo.merchant_id;

      if (useCodi && !paymentResponse.paymentSuccess) {
        if (paymentResponse.error.error.code === ErrorCodes.PURCHASE_LIMIT_PER_DAY) {
          this._errorsHandlerService.errorHandler(paymentResponse.error);
        }
        return;
      }

      if (useCodi && paymentResponse.paymentSuccess) {
        this._pagesFacade.setCurrentPage(Pages.other, OtherPaymentMethods.CODI);
        return;
      }
      if (paymentResponse.paymentSuccess) {
        this._paymentSuccessHandler(
          paymentResponse.response,
          checkoutInfo.next_quincena_enabled,
          amount,
          loanId,
          merchantId,
          useCodi
        );
      } else {
        this._errorsHandlerService.errorHandler(paymentResponse.error);
      }
    })
  );

  public _paymentSuccessHandler(
    { confirmUrl, type }: IPaymentResponsePayload,
    isNextQuincena: boolean,
    amount: number,
    loanId: number,
    merchantId: number,
    isCodi: boolean = false
  ): void | string | Promise<boolean> {
    const dataLayerParams = { identifier: loanId };
    if (type === PluginIntegrations.WOO || type === PluginIntegrations.POSUI) {
      if (isCodi) {
        return this._pagesFacade.setCurrentPage(Pages.other, OtherPaymentMethods.CODI_SUCCESS);
      }
      if (isNextQuincena) {
        return this._pagesFacade.setCurrentPage(
          Pages.thankyou,
          ThankyouPages.FIRST_INSTALLMENT_NEXT_FORNIGHT,
          dataLayerParams
        );
      } else {
        return this._pagesFacade.setCurrentPage(Pages.thankyou, ThankyouPages.FIRST_INSTALLMENT, dataLayerParams);
      }
    }

    if (type === PluginIntegrations.VIRTUAL_CARD) {
      this._virtualCardFacade.createVirtualCard(loanId, merchantId, amount);
      return this._router.navigate([`${OtherPages.virtualCard}`]);
    }

    if (type === PluginIntegrations.WALMART_CASHI) {
      return this._router.navigateByUrl(OtherPages.walmartCashi);
    }

    return (window.location.href = confirmUrl);
  }

  public reset(): void {
    this._paymentFacade.reset();
  }
}
