import { createAction, props } from '@ngrx/store';
import { ICheckoutInfo } from '../../interfaces/checkout-info.interface';

export const getInfoCheckout = createAction('[Checkout info] Get checkout info', props<{ id: string }>());

export const getInfoCheckoutSuccess = createAction(
  '[Checkout info] Get checkout info ¡SUCCESS!',
  props<{ data: ICheckoutInfo }>()
);

export const getInfoCheckoutError = createAction(
  '[Checkout info] Get checkout info ¡ERROR!',
  props<{ error: unknown }>()
);
