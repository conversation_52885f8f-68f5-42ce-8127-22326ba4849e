import { Params } from '@angular/router';
import { createAction, props } from '@ngrx/store';

export const setCurrentUrls = createAction('[Checkout Payment Module] Set Current Urls Init', props<{ currentUrl: string; currentUrlLogin: string; }>());

export const setCurrentPage = createAction('[Checkout Payment Module] Set Current Page', props<{ currentPage: string; currentPath?: string; queryParams?: Params}>());

export const goLoginPage = createAction('[Checkout Payment Module] Go to login');

export const reset = createAction('[Checkout Payment Module] Reset Page');
