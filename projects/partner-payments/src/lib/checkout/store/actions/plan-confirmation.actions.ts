import { IScheme } from '@aplazo/partner-core';
import { createAction, props } from '@ngrx/store';
import { PaymentTypes } from '../../../payment-methods/enums/payment-types.enum';
import { PaymentVersion } from '../../enums/payment-versions.enum';

export const selectScheme = createAction('[Checkout Payment Module - Plan Confirmation] Select id scheme', props<{ scheme: IScheme }>());

export const setPaymentType = createAction('[Checkout Payment Module - Plan Confirmation] Set Payment Type', props<{ paymentType: PaymentTypes }>());

export const setPaymentVersion = createAction('[Checkout Payment Module - Payment Orchestation] Set Payment Version', props<{ version: PaymentVersion }>());

export const setLoanInfo = createAction('[Checkout Payment Module - Plan Confirmation] Set Loan Info', props<{ loanId: number; merchantId: number; amount: number }>());

export const setUseAplazoPoints = createAction('[Checkout Payment Module - Plan Confirmation] Set Loan Info', props<{ useAplazoPoints: boolean; }>());

export const setUsePaymentCodi = createAction('[Checkout Payment Module - Plan Confirmation] Set CODI', props<{ usePaymentCodi: boolean; }>());

export const setFolioCodi = createAction('[Checkout Payment Module] Set Folio CODI', props<{ folioCodi: string; }>());

export const reset = createAction('[Checkout Payment Module - Plan Confirmation] Reset Plan Confirmation');
