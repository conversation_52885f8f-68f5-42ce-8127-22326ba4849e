import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action } from '@ngrx/store';
import { Observable, catchError, map, switchMap } from 'rxjs';
import * as checkoutInfoActions from '../actions/checkout-info.actions';
import { LoanService } from '../../services/loan/loan.service';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class CheckoutInfoEffects {
  constructor(private actions$: Actions, private _checkout$: LoanService) {}

  getCheckoutInfo$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(checkoutInfoActions.getInfoCheckout),
      switchMap(({ id }) =>
        this._checkout$.getCheckoutInfoById(id).pipe(
          map(data => checkoutInfoActions.getInfoCheckoutSuccess({ data })),
          catchError((error: HttpErrorResponse) => {
            return [checkoutInfoActions.getInfoCheckoutError({ error })];
          })
        )
      )
    )
  );
}
