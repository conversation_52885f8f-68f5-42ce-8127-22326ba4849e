import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action } from '@ngrx/store';
import { EMPTY, Observable } from 'rxjs';
import { switchMap, withLatestFrom } from 'rxjs/operators';
import * as pageActions from '../actions/page.actions';
import { PagesFacade } from '../facades/page.facede';

@Injectable({
  providedIn: 'root',
})
export class PageEffects {
  constructor(private actions$: Actions, private _router: Router, private _pageFacade: PagesFacade) {}

  changePage$: Observable<Action> = createEffect(
    () =>
      this.actions$.pipe(
        ofType(pageActions.setCurrentPage),
        withLatestFrom(this._pageFacade.currentUrl$),
        switchMap(([{ currentPage, currentPath, queryParams }, currentUrl]) => {
          void this._router.navigate([`/${currentUrl}/${currentPage}/${currentPath}`], { queryParams });
          return EMPTY;
        })
      ),
    { dispatch: false }
  );

  goLoginPage$: Observable<Action> = createEffect(
    () =>
      this.actions$.pipe(
        ofType(pageActions.goLoginPage),
        withLatestFrom(this._pageFacade.currentUrlLogin$),
        switchMap(([, currentUrl]) => {
          void this._router.navigate([`/${currentUrl}`]);
          return EMPTY;
        })
      ),
    { dispatch: false }
  );
}
