import { Injectable } from '@angular/core';
import { Store, select } from '@ngrx/store';
import * as checkoutInfoActions from '../actions/checkout-info.actions';
import { CheckoutPaymentState } from '../state/checkout.state';
import * as checkoutInfoSelector from '../selectors/checkout-info.selector';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export class CheckoutInfoFacade {
  constructor(private store: Store<CheckoutPaymentState>) {}

  public getInfoCheckout(id: string) {
    if (!id) {
      const error: HttpErrorResponse = new HttpErrorResponse({
        error: 'Invalid id provided.',
        status: 400,
        statusText: 'Bad Request',
      });
      console.error('checkout: ', id);
      this.store.dispatch(checkoutInfoActions.getInfoCheckoutError({ error }));
      return;
    }
    this.store.dispatch(checkoutInfoActions.getInfoCheckout({ id }));
  }

  public checkoutInfo$ = this.store.pipe(select(checkoutInfoSelector.checkoutInfoSelector));

  public refreshCheckout$(): void {
    const token = localStorage.getItem('TOKEN_LOAN');
    this.getInfoCheckout(token);
  }
}
