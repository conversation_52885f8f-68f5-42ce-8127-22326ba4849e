
import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import * as pageActions from '../actions/page.actions';
import { CheckoutPaymentState } from '../state/checkout.state';
import * as pagesSelectors from '../selectors/page.selectors';
import { Params } from '@angular/router';


@Injectable({ providedIn: 'root' })
export class PagesFacade {

  constructor(private store: Store<CheckoutPaymentState>) {}

  public currentUrl$ = this.store
    .select(pagesSelectors.currentUrlSelector);

  public currentUrlLogin$ = this.store
    .select(pagesSelectors.currentUrlLoginSelector);

  public setCurrentUrls(currentUrl = 'payment', currentUrlLogin = 'login'): void {
    this.store.dispatch(pageActions.setCurrentUrls({ currentUrl, currentUrlLogin }));
  }

  public setCurrentPage(currentPage: string, currentPath = '', queryParams?: Params ): void {
    this.store.dispatch(pageActions.setCurrentPage({ currentPage, currentPath, queryParams }));
  }

  public goLoginPage(): void {
    this.store.dispatch(pageActions.goLoginPage());
  }
}
