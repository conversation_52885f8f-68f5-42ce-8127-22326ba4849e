import { Injectable } from '@angular/core';
import { IScheme } from '@aplazo/partner-core';
import { Store } from '@ngrx/store';
import { filter } from 'rxjs';
import { PaymentTypes } from '../../../payment-methods/enums/payment-types.enum';
import * as planConfirmationActions from '../actions/plan-confirmation.actions';
import * as checkoutSelectors from '../selectors/checkout.selectors';
import { CheckoutPaymentState } from '../state/checkout.state';

@Injectable({ providedIn: 'root' })
export class PlanConfirmationFacade {
  constructor(private store: Store<CheckoutPaymentState>) { }

  public scheme$ = this.store.select(checkoutSelectors.schemeSelector).pipe(filter(scheme => !!scheme));

  public paymentType$ = this.store
    .select(checkoutSelectors.paymentTypeSelector)
    .pipe(filter(paymentType => !!paymentType));

  public loanId$ = this.store.select(checkoutSelectors.loanIdSelector).pipe(filter(loanId => !!loanId));

  public merchantId$ = this.store.select(checkoutSelectors.merchantIdSelector).pipe(filter(merchantId => !!merchantId));

  public amount$ = this.store.select(checkoutSelectors.amountSelector);

  public useAplazoPoints$ = this.store.select(checkoutSelectors.useAplazoPointsSelector);

  public usePaymentCodi$ = this.store.select(checkoutSelectors.usePaymentCodiSelector);

  public folioCodi$ = this.store.select(checkoutSelectors.folioCodiSelector);

  public paymentVersion$ = this.store
    .select(checkoutSelectors.paymentVersionSelector)
    .pipe(filter(version => !!version));

  public setLoanInfo(loanId: number, merchantId: number, amount: number): void {
    this.store.dispatch(planConfirmationActions.setLoanInfo({ loanId, merchantId, amount }));
  }

  public setSchemeSelected(scheme: IScheme): void {
    this.store.dispatch(planConfirmationActions.selectScheme({ scheme }));
  }

  public setPaymentType(paymentType: PaymentTypes): void {
    this.store.dispatch(planConfirmationActions.setPaymentType({ paymentType }));
  }

  public setUseAplazoPoints(useAplazoPoints: boolean): void {
    this.store.dispatch(planConfirmationActions.setUseAplazoPoints({ useAplazoPoints }));
  }

  public setUsePaymentCodi(usePaymentCodi: boolean): void {
    this.store.dispatch(planConfirmationActions.setUsePaymentCodi({ usePaymentCodi }));
  }

  public setFolioCodi(folioCodi: string): void {
    this.store.dispatch(planConfirmationActions.setFolioCodi({ folioCodi }));
  }

  public reset(): void {
    this.store.dispatch(planConfirmationActions.reset());
  }
}
