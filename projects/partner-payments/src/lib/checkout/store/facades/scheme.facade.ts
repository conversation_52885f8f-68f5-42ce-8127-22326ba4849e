import { Injectable } from '@angular/core';
import { Store, select } from '@ngrx/store';
import * as schemeActions from '../actions/scheme.actions';
import { CheckoutPaymentState } from '../state/checkout.state';

import * as schemeSelector from '../selectors/scheme.selector';
import { IScheme } from '../../interfaces/checkout-info.interface';

@Injectable({ providedIn: 'root' })
export class SchemeFacade {
  constructor(private store: Store<CheckoutPaymentState>) {}

  public setInfoCheckout(scheme: IScheme) {
    this.store.dispatch(schemeActions.setScheme({ scheme }));
  }

  public getSchemeSelected$ = this.store.pipe(select(schemeSelector.schemeSelector));
}
