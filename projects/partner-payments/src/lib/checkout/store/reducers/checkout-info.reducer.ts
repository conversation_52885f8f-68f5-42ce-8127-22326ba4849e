import { Action, createReducer, on } from '@ngrx/store';
import * as checkoutInfoActions from '../actions/checkout-info.actions';
import { ICheckoutInfo } from '../../interfaces/checkout-info.interface';

const initialState: ICheckoutInfo = null;

export const _checkoutInfoReducer = createReducer(
  initialState,
  on(checkoutInfoActions.getInfoCheckoutSuccess, (_, { data }) => ({ ...data })),
  on(checkoutInfoActions.getInfoCheckoutError, (_, { error }) => ({ ..._, error }))
);

export function checkoutInfoReducer(state: ICheckoutInfo, action: Action) {
  return _checkoutInfoReducer(state, action);
}
