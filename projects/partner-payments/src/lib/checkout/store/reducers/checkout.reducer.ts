import { ActionReducerMap } from '@ngrx/store';
import { CheckoutPaymentState } from '../state/checkout.state';
import { pagesReducer } from './page.reducer';
import { planConfirmationReducer } from './plan-confirmation.reducer';
import { checkoutInfoReducer } from './checkout-info.reducer';
import { schemeReducer } from './scheme.reducer';

export const checkoutPaymentReducer: ActionReducerMap<CheckoutPaymentState> = {
  page: pagesReducer,
  planConfirmation: planConfirmationReducer,
  checkoutInfo: checkoutInfoReducer,
  scheme: schemeReducer
};
