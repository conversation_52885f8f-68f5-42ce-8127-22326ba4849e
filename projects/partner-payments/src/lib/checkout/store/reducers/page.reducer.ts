import { Action, createReducer, on } from '@ngrx/store';
import { Pages } from '../../enums/pages.enum';
import { IPage } from '../../interfaces/page.interface';
import * as pageActions from '../actions/page.actions';

export const initialState: IPage = {
  currentUrl: 'payment',
  currentUrlLogin: 'login',
  currentPage: Pages.default,
  currentPath: ''
};

const _pagesReducer = createReducer(
  initialState,
  on(pageActions.setCurrentUrls, (state, { currentUrl, currentUrlLogin }) => ({ ...state, currentUrl, currentUrlLogin })),
  on(pageActions.setCurrentPage, (state, { currentPage, currentPath, queryParams }) => ( { ...state, currentPage, currentPath, queryParams } )),
  on(pageActions.reset, () => initialState)
);

export function pagesReducer(state: IPage, action: Action): IPage {
  return _pagesReducer(state, action);
}
