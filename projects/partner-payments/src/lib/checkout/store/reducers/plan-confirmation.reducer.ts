import { Action, createReducer, on } from '@ngrx/store';
import * as planConfirmationActions from '../actions/plan-confirmation.actions';
import { IPlanConfirmation } from '../../interfaces/plan-confirmation.interface';
import { PaymentTypes } from '../../../payment-methods/enums/payment-types.enum';

export const initialState: IPlanConfirmation = {
  scheme: null,
  paymentType: PaymentTypes.firstInstallmentNextFornight,
  loanId: null,
  version: null,
  amount: null,
  merchantId: null,
  useAplazoPoints: null,
  usePaymentCodi: null,
  folioCodi: null,
};

const _planConfirmationReducer = createReducer(
  initialState,
  on(planConfirmationActions.setPaymentVersion, (state, { version }) => ({ ...state, version })),
  on(planConfirmationActions.selectScheme, (state, { scheme }) => ({ ...state, scheme })),
  on(planConfirmationActions.setUseAplazoPoints, (state, { useAplazoPoints }) => ({ ...state, useAplazoPoints })),
  on(planConfirmationActions.setUsePaymentCodi, (state, { usePaymentCodi }) => ({ ...state, usePaymentCodi })),
  on(planConfirmationActions.setFolioCodi, (state, { folioCodi }) => ({ ...state, folioCodi })),
  on(planConfirmationActions.setPaymentType, (state, { paymentType }) => ({ ...state, paymentType })),
  on(planConfirmationActions.setLoanInfo, (state, { loanId, merchantId, amount }) => ({
    ...state,
    loanId,
    merchantId,
    amount,
  })),
  on(planConfirmationActions.reset, () => initialState)
);

export function planConfirmationReducer(state: IPlanConfirmation, action: Action): IPlanConfirmation {
  return _planConfirmationReducer(state, action);
}
