import { Action, createReducer, on } from '@ngrx/store';
import * as schemeActions from '../actions/scheme.actions';
import { IScheme } from '../../interfaces/checkout-info.interface';

const initialState: IScheme = null;

export const _schemeReducer = createReducer(
  initialState,
  on(schemeActions.setScheme, (_, { scheme }) => scheme)
);

export function schemeReducer(state: IScheme, action: Action) {
  return _schemeReducer(state, action);
}
