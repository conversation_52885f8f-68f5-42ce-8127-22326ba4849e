import { createFeatureSelector, createSelector } from '@ngrx/store';
import { checkoutPaymentFeatureName, CheckoutPaymentState } from '../state/checkout.state';

export const checkoutRootSelector = createFeatureSelector<CheckoutPaymentState>(checkoutPaymentFeatureName);

export const schemeSelector = createSelector(checkoutRootSelector, state => state.planConfirmation.scheme);

export const paymentTypeSelector = createSelector(checkoutRootSelector, state => state.planConfirmation.paymentType);

export const loanIdSelector = createSelector(checkoutRootSelector, state => state.planConfirmation.loanId);

export const useAplazoPointsSelector = createSelector(
  checkoutRootSelector,
  state => state.planConfirmation.useAplazoPoints
);

export const usePaymentCodiSelector = createSelector(
  checkoutRootSelector,
  state => state.planConfirmation.usePaymentCodi
);

export const folioCodiSelector = createSelector(
  checkoutRootSelector,
  state => state.planConfirmation.folioCodi
);

export const merchantIdSelector = createSelector(checkoutRootSelector, state => state.planConfirmation.merchantId);

export const amountSelector = createSelector(checkoutRootSelector, state => state.planConfirmation.amount);

export const paymentVersionSelector = createSelector(checkoutRootSelector, state => state.planConfirmation.version);
