import { createFeatureSelector, createSelector } from '@ngrx/store';
import { checkoutPaymentFeatureName, CheckoutPaymentState } from '../state/checkout.state';

export const checkoutRootSelector =
  createFeatureSelector<CheckoutPaymentState>(checkoutPaymentFeatureName);

export const currentUrlSelector = createSelector(
  checkoutRootSelector,
  state => state.page.currentUrl
);

export const currentUrlLoginSelector = createSelector(
  checkoutRootSelector,
  state => state.page.currentUrlLogin
);