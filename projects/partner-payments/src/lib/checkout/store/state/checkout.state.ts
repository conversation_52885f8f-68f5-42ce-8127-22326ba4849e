import { ICheckoutInfo, IScheme } from '../../interfaces/checkout-info.interface';
import { IPage } from '../../interfaces/page.interface';
import { IPlanConfirmation } from '../../interfaces/plan-confirmation.interface';

export const checkoutPaymentFeatureName = 'checkoutPaymentModule';

export type CheckoutPaymentState = {
  page: IPage;
  planConfirmation: IPlanConfirmation;
  checkoutInfo: ICheckoutInfo;
  scheme: IScheme;
};
