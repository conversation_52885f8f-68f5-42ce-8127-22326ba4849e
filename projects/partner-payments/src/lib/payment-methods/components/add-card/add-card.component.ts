import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialog as MatDialog,
} from '@angular/material/legacy-dialog';
import { LoginCoreFacade, PARTNER_CORE_ENVIRONMENT, PartnerCoreEnvironment, UiCoreFacade } from '@aplazo/partner-core';
import { TranslocoService } from '@ngneat/transloco';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { IAddCardComponent, ICardValid } from '../../interfaces/add-card.interface';
import { IVgsErrorCodes, VGSFormFields, VGSResponse } from '../../interfaces/vgs.interface';
import { OpenpayService } from '../../services/payment-providers/openpay/openpay.service';
import { ErrorsHandlerService } from '../../../checkout/services/errors-handler/errors-handler.service';
import { SuccessModalComponent } from '../templates/success-modal/success-modal.component';
import { Overlay } from '@angular/cdk/overlay';
import { CardService } from '../../services/card/card.service';

@Component({
  selector: 'aplazo-payments-add-card',
  templateUrl: './add-card.component.html',
  styleUrls: ['./add-card.component.scss'],
})
export class AddCardComponent implements OnInit, OnDestroy {
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  public formGroup: UntypedFormGroup;

  public txtTemplate: Record<string, any> = null;

  public errorsForm: IVgsErrorCodes = null;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: IAddCardComponent,
    @Inject(PARTNER_CORE_ENVIRONMENT)
    public environment: PartnerCoreEnvironment,
    public overlay: Overlay,
    public _dialogRef: MatDialogRef<AddCardComponent>,
    private _uiFacade: UiCoreFacade,
    private _translocoService: TranslocoService,
    private _openPayService: OpenpayService,
    private _errorsHandlerService: ErrorsHandlerService,
    private _loginFacade: LoginCoreFacade,
    private _dialog: MatDialog,
    private _cardService: CardService
  ) {}

  public ngOnInit(): void {
    this._translocoService
      .selectTranslateObject('PAYMENT_METHODS_MODULE.ADD_CARD')
      .pipe(
        takeUntil(this._unsubscribe$),
        switchMap(textTemplate => {
          this.txtTemplate = textTemplate;
          return this._translocoService.selectTranslateObject('ERRORS.INPUTS');
        })
      )
      .subscribe(errorInputs => {
        this.errorsForm = {
          1001: errorInputs.required,
          1011: errorInputs.invalid,
          1015: errorInputs.invalid,
          1017: errorInputs.invalid,
        };
      });
    this._openPayService._addOpenpayScripts();
  }

  public onSubmit({ data, form }): void {
    this._uiFacade.setLoading(true);
    this._loginFacade.getAuthToken$.pipe(takeUntil(this._unsubscribe$)).subscribe(token => {
      this.postVGS({ data, form, token });
    });
  }

  postVGS({ data, form, token }) {
    this._cardService._isCardValid$(data.bin.slice(0, 8)).subscribe((responseCard: ICardValid) => {
      if (responseCard.isValid) {
        form.submit(
          '/customer/card/token',
          {
            method: 'POST',
            data: values => {
              return {
                card_security_code: values[VGSFormFields.CardSecurityCode],
                // card_security_code: values[VGSFormFields.CardExpirationDate], // works to test errors :)
                card_number: values[VGSFormFields.CardNumber],
                card_expiration_date: values[VGSFormFields.CardExpirationDate],
                cardholder_name: data.name,
                card_type: responseCard?.cardType?.toUpperCase() ?? '',
                platform: 'ONLINE',
                brand: responseCard?.brand ?? '',
                bin: data.bin,
                device_session: this._openPayService.getDeviceSession(),
              };
            },
            headers: {
              authorization: `Bearer ${token}`,
              'x-device-type': 'web',
              platform: 'web',
            },
          },
          (status, response) => this.handleVGSResponse(status, response)
        );
      } else {
        this.handleError(4500, {
          code: '4500',
          error: 'invalid card',
        });
      }
    });
  }

  private handleVGSResponse = (status, response: any) => {
    if (response?.code === 1) {
      this.handleSuccess();
    } else {
      if (this.data?.isCustomer) {
        this._dialogRef.close(false);
      } else {
        this.handleError(status, response);
      }
    }
  };

  private handleSuccess(): void {
    this._dialogRef.close(true);
    const data = {
      description: this.data.hasCards ? this.txtTemplate?.modal?.successReplace : this.txtTemplate?.modal?.success,
      img: 'https://aplazoassets.s3.us-west-2.amazonaws.com/messages-images/success-circle.png', // TODO MOVE to textTemplate,
      time: this.txtTemplate?.modal?.timeInSeconds,
    };
    this._dialog.open(SuccessModalComponent, {
      disableClose: false,
      maxWidth: '350px',
      minHeight: '350px',
      width: '100%',
      hasBackdrop: true,
      autoFocus: false,
      data: data,
      scrollStrategy: this.overlay.scrollStrategies.noop(),
    });
  }

  private handleError(status, error: any): void {
    this._dialogRef.close(false);
    const code = error?.code ?? status;
    this._errorsHandlerService.errorHandler({
      status: code,
      error: error,
    });
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
