<aplazo-header *ngIf="txtTemplate" [showHeader]="showHeader" [title]="txtTemplate.header.title"></aplazo-header>
<section class="aplazo-add-card-template__info">
  <div class="aplazo-add-card-template__info-item">
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/icons/card_in_cart.svg" alt="" />
    <p>{{ txtTemplate.cardInfo.first }}</p>
  </div>
  <div class="aplazo-add-card-template__info-item">
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/icons/card_in_purse.svg" alt="" />
    <p>{{ txtTemplate.cardInfo.second }}</p>
  </div>
</section>
<div class="aplazo-form-card">
  <!-- card holder name -->
  <div class="aplazo-form-card-input">
    <label [class.red]="nameControl.touched && !nameControl.valid">{{ txtTemplate.cardName.label }}</label>
    <div [id]="VGSFormFields.CardholderName" class="aplazo-form-card-input-wrapper">
      <input
        type="text"
        [formControl]="nameControl"
        [placeholder]="txtTemplate.cardName.placeholder"
        [class.invalid]="nameControl.touched && !nameControl.valid"
        [class]="{
          invalid: nameControl.touched && !nameControl.valid,
          dirty: nameControl.touched && nameControl.dirty
        }"
        class="aplazo-form-card-input-wrapper-item"
      />
    </div>
    <ng-container *ngIf="nameControl.touched">
      <p class="aplazo-form-card-input-errors" *ngIf="nameControl.hasError('required')">
        {{ errorsForm[1001] }}
      </p>
      <p class="aplazo-form-card-input-errors" *ngIf="nameControl.hasError('pattern')">
        {{ 'ERRORS.INPUTS.invalidFormat' | transloco }}
      </p>
    </ng-container>
  </div>
  <!-- card number -->
  <div class="aplazo-form-card-input">
    <label [class.red]="errors['card-number']?.isTouched && !errors['card-number']?.isValid">{{
      txtTemplate.cardNumber.label
    }}</label>
    <div [id]="VGSFormFields.CardNumber" class="aplazo-form-card-input-wrapper"></div>
    <p
      class="aplazo-form-card-input-errors"
      *ngIf="errors['card-number']?.isTouched && !errors['card-number']?.isValid"
    >
      {{ errorsForm[errors['card-number'].errors[0]] }}
    </p>
  </div>
  <!-- date -->
  <div class="aplazo-form-card-group">
    <div class="aplazo-form-card-input">
      <label [class.red]="errors['card-expiration-date']?.isTouched && !errors['card-expiration-date']?.isValid">{{
        txtTemplate.expiration.label
      }}</label>
      <div [id]="VGSFormFields.CardExpirationDate" class="aplazo-form-card-input-wrapper"></div>
      <p
        class="aplazo-form-card-input-errors"
        *ngIf="errors['card-expiration-date']?.isTouched && !errors['card-expiration-date']?.isValid"
      >
        {{ errorsForm[errors['card-expiration-date'].errors[0]] }}
      </p>
    </div>
    <!-- cvv -->
    <div class="aplazo-form-card-input">
      <label [class.red]="errors['card-security-code']?.isTouched && !errors['card-security-code']?.isValid">{{
        txtTemplate.cvc.label
      }}</label>
      <div [id]="VGSFormFields.CardSecurityCode" class="aplazo-form-card-input-wrapper"></div>
      <p
        class="aplazo-form-card-input-errors"
        *ngIf="errors['card-security-code']?.isTouched && !errors['card-security-code']?.isValid"
      >
        {{ errorsForm[errors['card-security-code'].errors[0]] }}
      </p>
    </div>
  </div>

  <aplazo-button
    [size]="'normal'"
    [title]="hasCards ? txtTemplate.replace : txtTemplate.add"
    [isDisabled]="isDisabled || this.nameControl.invalid"
    [isRounded]="true"
    [isFullWidth]="true"
    (click)="submitFormVGS()"
  ></aplazo-button>
</div>
<section *ngIf="txtTemplate" class="aplazo-add-card-template__payment-methods">
  <p class="aplazo-add-card-template__payment-methods-text">
    {{ txtTemplate.paymentMethods }}
  </p>
  <div class="aplazo-add-card-template__payment-methods-logos">
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/ebanx.svg" alt="Ebanx" />
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/openpay_color.png" alt="OpenPay" />
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/conekta.svg" alt="Conekta" />
    <img src="https://aplazoassets.s3.us-west-2.amazonaws.com/kushki.svg" alt="Kushki" />
  </div>
  <p class="aplazo-add-card-template__payment-methods-advice">
    {{ txtTemplate.paymentText }}
  </p>
</section>
