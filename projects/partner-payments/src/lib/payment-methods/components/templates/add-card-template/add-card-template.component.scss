@import '@aplazo/partner-styles/src/scss/_typography';

.aplazo__form {
  padding-bottom: 74px;
}

.aplazo-add-card-template {
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  &__error {
    padding-bottom: 25px;
  }

  &__errors {
    margin-bottom: 40px;
  }

  &__icon {
    width: 40px;

    &-image {
      width: 100%;
      border-radius: 4px;
    }
  }

  &__info {
    font-family: var(--font-semibold);
    font-size: 13px;
    line-height: 2ch;
    background-color: var(--color-gray-1);
    margin-bottom: 1em;
    border-radius: 8px;
    padding: 1em;
    &-item {
      margin: 0.5em 0;
      display: flex;
      & p {
        text-align: justify;
        padding: 0 1em;
      }
    }
  }

  &__payment-methods {
    @include body-small();
    background-color: var(--color-gray-1);
    color: var(--color-gray-4);
    padding: 25px 20px;
    border-radius: 8px;
    letter-spacing: -0.25px;
    border-radius: 8px;

    &-text {
      font-size: 11px;
      word-break: keep-all;
    }

    &-advice {
      color: var(--color-black);
    }

    &-logos {
      display: flex;
      justify-content: space-around;
      flex-wrap: nowrap;
      padding-bottom: 1rem;

      & img {
        max-height: 2rem;
        margin-top: 1rem;
        max-width: 3.5rem;
      }
    }
  }
}

.mask-text-password {
  -webkit-text-security: disc;
  -moz-webkit-text-security: disc;
  -moz-text-security: disc;
}

.aplazo__form {
  padding-bottom: 2em;
}

.aplazo-form-card {
  &-group {
    gap: 10px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    & > * {
      width: 47.5%;
    }
  }
  &-input {
    & label {
      text-align: left;
      display: block;
      font-size: 12px;
      color: var(--color-black);
      &.red {
        color: red;
      }
    }
    &-errors {
      font-size: 12px;
      color: red;
      text-align: left;
      padding-left: 15px;
    }
    &-wrapper {
      font-size: 12px;
      width: 100%;
      display: flex;
      flex-direction: column;
      height: 45px;
      overflow: hidden;
      position: relative;
      &-item {
        outline: none;
        font-size: 16px;
        color: rgb(64, 84, 95);
        border: 1px solid rgb(222, 227, 238);
        border-radius: 50px;
        padding: 0px 15px;
        height: 40px;
        width: 100%;
        max-width: 350px;
        display: block;
        &::placeholder {
          color: var(--color-gray-4);
        }
        &.invalid {
          color: red;
          border-color: red;
          &::placeholder {
            color: red;
          }
        }
        &:hover,
        &:focus,
        &.dirty {
          border-color: var(--color-gray-4);
        }
      }
    }
  }
}
