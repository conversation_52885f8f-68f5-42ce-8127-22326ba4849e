import { Component, EventEmitter, Inject, Input, NgZone, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ICardAdded } from '@aplazo/web-ui';
import { IErrorsInterface, IVgsErrorCodes, VGSConfigValues, VGSFormFields } from '../../../interfaces/vgs.interface';
import { loadVGSCollect } from '@vgs/collect-js';
import { PARTNER_CORE_ENVIRONMENT, PartnerCoreEnvironment } from '@aplazo/partner-core';

@Component({
  selector: 'aplazo-payments-add-card-template',
  templateUrl: './add-card-template.component.html',
  styleUrls: ['./add-card-template.component.scss'],
})
export class AddCardTemplateComponent implements OnInit {
  @Input() txtTemplate: any;

  @Input() formGroup: UntypedFormGroup;

  @Input() showHeader = true;

  @Input() hasCards = false;

  @Input() btnSubmitDisabled = false;

  @Input() card: ICardAdded;

  @Output() submitForm: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() btnBack: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input() errorsForm: IVgsErrorCodes;

  @Output() formEmit: EventEmitter<any> = new EventEmitter<any>();

  public nameControl: UntypedFormControl;

  public errors: IErrorsInterface;

  public isDisabled = true;

  public form: any;

  public bin: string;

  public VGSFormFields = VGSFormFields;

  public VGSConfig: VGSConfigValues = null;

  CSS = {
    'font-size': '16px',
    color: '#40545F',
    '&::placeholder': {
      color: '#78909c',
    },
    '&:hover,&:focus,&.dirty': {
      'border-color': '#78909c',
    },
    border: '1px solid #dee3ee',
    'border-radius': '50px',
    padding: '0 15px',
    height: '40px',
    width: '90%',
    'max-width': '327px',
    '&.invalid.touched': {
      color: 'red',
      border: '1px solid red',
      '&::placeholder': {
        color: 'red',
      },
      '&:hover': {
        'border-color': '#78909c',
      },
    },
  };

  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    public environment: PartnerCoreEnvironment,
    private readonly formBuilder: UntypedFormBuilder,
    private ngZone: NgZone
  ) {
    const regexCardName = new RegExp(/^[a-zA-ZñÑáéíóúÁÉÍÓÚ]+(?:\s[a-zA-ZñÑáéíóúÁÉÍÓÚ]+)*$/);
    this.nameControl = this.formBuilder.control(null, [Validators.required, Validators.pattern(regexCardName)]);
    this.errors = {
      'cardholder-name': undefined,
      'card-number': undefined,
      'card-expiration-date': undefined,
      'card-security-code': undefined,
    };
    this.VGSConfig = {
      vaultId: this.environment.VGS.tenantId,
      environment: this.environment.VGS.environment,
      // version: this.environment.VGS.version, // last version 2.19.0 new version allow 3 more digits in card input => https://www.verygoodsecurity.com/docs/vgs-collect/js/changelog/#change
      version: '2.20.0', // last version 2.19.0 new version allow 3 more digits in card input => https://www.verygoodsecurity.com/docs/vgs-collect/js/changelog/#change
    };
    console.info('vgs: ', this.VGSConfig.version);
  }

  ngOnInit(): void {
    this.collectData();
  }

  public async collectData() {
    const collect = await loadVGSCollect({
      vaultId: this.VGSConfig.vaultId,
      environment: this.VGSConfig.environment,
      version: this.VGSConfig.version,
    }).catch(error => {
      console.error(error);
    });
    this.form = (collect as Record<string, any>).init((state: Record<VGSFormFields, any>) => {
      this.ngZone.run(() => {
        this.saveBin(state);
        Object.entries(state).forEach(([key, value]: [VGSFormFields, any]) => {
          this.errors[key] = {
            errors: value?.errors.map(e => e.code),
            isTouched: value?.isTouched,
            isValid: value?.isValid,
          };
        });
        this.isDisabled = Object.values(state).some(value => !value.isValid);
      });
    });
    this.form.field(`#${VGSFormFields.CardNumber}`, {
      name: `${VGSFormFields.CardNumber}`,
      type: 'card-number',
      placeholder: this.txtTemplate.cardNumber.placeholder,
      validations: ['required', 'validCardNumber'],
      showCardIcon: true,
      autoComplete: 'cc-number',
      css: {
        ...this.CSS,
        width: '100%',
        'max-width': '350px',
      },
    });
    this.form.field(`#${VGSFormFields.CardSecurityCode}`, {
      name: `${VGSFormFields.CardSecurityCode}`,
      type: 'card-security-code',
      placeholder: this.txtTemplate.cvc.placeholder,
      validations: ['required', 'validCardSecurityCode'],
      autoComplete: 'cc-csc',
      css: {
        ...this.CSS,
        width: '130px',
      },
      hideValue: true,
    });
    this.form.field(`#${VGSFormFields.CardExpirationDate}`, {
      name: `${VGSFormFields.CardExpirationDate}`,
      type: 'card-expiration-date',
      placeholder: this.txtTemplate.expiration.placeholder,
      validations: ['required', 'validCardExpirationDate'],
      autoComplete: 'cc-exp',
      yearLength: 2,
      css: {
        ...this.CSS,
        width: '130px',
      },
    });
  }

  public onSubmit(): void {
    this.formGroup.markAllAsTouched();
    this.submitForm.emit(true);
  }

  public clickBack(): void {
    this.btnBack.emit(true);
  }

  public saveBin(state: Record<VGSFormFields, any>) {
    const cardNumber = state['card-number'];
    this.bin = cardNumber?.bin;
  }

  public submitFormVGS() {
    if (this.isDisabled) {
      return;
    }
    const VGSDataCollect = {
      data: {
        name: this.nameControl.value,
        bin: this.bin,
      },
      form: this.form,
    };

    this.formEmit.emit(VGSDataCollect);
  }
}
