<div class="aplazo-list-cards-added__header">
  <h6>{{ txtTemplate.title }}</h6>
</div>
<div
  class="aplazo-list-cards-added__add-card"
  *ngIf="!card && paymentMethodsEnabled?.includes('card')"
  (click)="replaceCard()"
>
  <img [src]="genericCard" />
  <p>{{ txtTemplate.addCard | uppercase }}</p>
</div>
<form [formGroup]="formGroup" (ngSubmit)="onSubmit()" autocomplete="off">
  <ng-container>
    <section class="aplazo-list-cards-added">
      <mat-radio-group formControlName="paymentMethodSelected">
        <!-- CODI -->
        <div
          class="aplazo-option-payment-method__codi-points"
          *ngIf="paymentMethodsEnabled?.includes('codi') && showPromoAplazoPoints"
        >
          <img [src]="txtTemplate.coinsStackColor" alt="" />
          <p [innerHTML]="txtTemplate.earnPoints_1 + promoCashback + txtTemplate.earnPoints_2"></p>
        </div>
        <div class="aplazo-option-payment-method__content" *ngIf="paymentMethodsEnabled?.includes('codi')">
          <mat-radio-button class="aplazo-option-payment-method__option" value="codi">
            <img
              class="aplazo-option-payment-method__image aplazo-option-payment-method__image__codi"
              [src]="txtTemplate.codiImage"
              alt="type"
            />
            <p class="aplazo-option-payment-method__payment-name" [innerHTML]="txtTemplate.codiReg"></p>
          </mat-radio-button>
        </div>
        <ng-container *ngIf="paymentMethodsEnabled?.includes('card') && card">
          <div class="aplazo-option-payment-method__content">
            <mat-radio-button [value]="card.id" class="aplazo-option-payment-method__option">
              <div class="aplazo-option-payment-method__images">
                <img
                  class="aplazo-option-payment-method__image"
                  [src]="cardsWhite[card?.brand?.toLowerCase()] ?? cardsColor['none']"
                  alt="type"
                />
              </div>
              <p *ngIf="card.paymentMethod === 'card'">••••{{ card.paymentMethodText }}</p>
              <p class="aplazo-option-payment-method__replace-card" (click)="replaceCard()">
                {{ card ? txtTemplate.replace : txtTemplate.add }}
              </p>
            </mat-radio-button>
          </div>
        </ng-container>
      </mat-radio-group>
    </section>
  </ng-container>
  <p
    class="aplazo-list-cards-added__input_padding"
    *ngIf="showErrorCardReplaced && formGroup?.get('paymentMethodSelected').value === 'card'"
  >
    {{ txtTemplate.errorReplaceCard }}
  </p>

  <!-- APLAZO POINTS -->
  <div class="aplazo-list-cards-added__aplazo-points" *ngIf="showCheckAplazoPoints">
    <mat-checkbox formControlName="useAplazoPoints" (change)="onUseAplazoPoints()">
      <img [src]="txtTemplate.coinsStackNormal" alt="aplazo puntos" />
      <span>{{ txtTemplate.useAplazoPoints1 }} {{ availablePoints }}{{ txtTemplate.useAplazoPoints2 }}</span>
    </mat-checkbox>
  </div>

  <p [class.aplazo-list-cards-added__button]="buttonFixed">
    <aplazo-button
      [size]="'normal'"
      [title]="btnPaymentText"
      [isDisabled]="btnSubmitDisabled || paymentMethodsEnabled.length === 0"
      [isRounded]="true"
      [isFullWidth]="true"
    ></aplazo-button>
  </p>
</form>
