@import '@aplazo/partner-styles/src/scss/_functions';

.aplazo-list-cards-added {
  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    align-items: center;
  }

  &__input_padding {
    padding-bottom: 1rem;
  }

  &__button {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: white;
    text-align: center;
    padding: 1.5rem;
    border: 1px solid #eceff1;
    box-shadow: 0px -4px 6px rgba(186, 199, 213, 0.25);
    z-index: 1000;
  }

  &__add-card {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px var(--color-black) solid;
    border-radius: 2rem;
    padding: 10px 0;
    margin-bottom: 1rem;
    cursor: pointer;
    img {
      max-width: 24px;
    }
    p {
      font-family: var(--font-bold);
      color: var(--color-black);
      margin-left: 8px;
    }
  }

  &__aplazo-points {
    display: flex;
    justify-content: left;
    align-items: center;
    margin-bottom: 2rem;
    img {
      max-width: 18px;
      margin: 0 20px 0 10px;
    }
    span {
      color: var(--color-black);
    }
  }
}

.mask-text-password {
  -webkit-text-security: disc;
  -moz-webkit-text-security: disc;
  -moz-text-security: disc;
}

.aplazo-option-payment-method {
  &__content {
    border-radius: 2rem;
    border: 1px solid var(--color-gray-2);
    color: var(--color-black);
    cursor: pointer;
    font-size: 1rem;
    padding: 0.7rem 1.25rem;
    min-height: pixelstorem(60);
    width: 100%;
    align-items: center;
    margin-bottom: 1rem;
  }

  &__option {
    display: flex;

    &_margin {
      margin: 0 pixelstorem(12);
    }
  }

  &__payment-name {
    font-size: 1rem;
    margin-right: 1rem;
  }

  &__images {
    display: flex;
  }

  &__image {
    max-height: 28px;
    width: auto;
    margin-right: 12px;
    &:not(:first-of-type) {
      margin-left: 0.5rem;
    }
  }

  &__replace-card {
    color: #272937;
    font-size: 14px;
    text-decoration: underline;
    cursor: pointer;
    font-family: var(--font-semibold);
    margin-left: auto;
  }
  &__codi-points {
    display: flex;
    flex-direction: row;
    img {
      height: 100%;
      max-height: 18px;
      width: auto;
    }
    p {
      font-family: var(--font-bold);
      color: var(--color-black);
      margin-left: 5px;
      font-size: 12px;
    }
  }
}

::ng-deep {
  .mat-checkbox-checked.mat-accent .mat-checkbox-ripple .mat-ripple-element {
    opacity: 0.03 !important;
    background-color: var(--color-black) !important;
  }

  .mat-checkbox-checked.mat-accent .mat-checkbox-background,
  .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: var(--color-black);
  }
  mat-radio-button .mdc-form-field {
    width: 100%;

    .mdc-radio {
      padding: 0;

      .mdc-radio__background > div {
        border-color: var(--color-black) !important;
      }
    }

    label {
      display: flex;
      padding-left: 8px;
      width: 100%;
    }
  }

  mat-radio-button .mat-radio-label-content {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    align-items: center;
  }

  .aplazo-header__description {
    display: none; // description is not used and break the design
  }
  .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--color-black);
  }
  .mat-radio-button.mat-accent .mat-radio-inner-circle,
  .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element:not(.mat-radio-persistent-ripple),
  .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-persistent-ripple,
  .mat-radio-button.mat-accent:active .mat-radio-persistent-ripple {
    background-color: var(--color-black) !important;
  }
}
