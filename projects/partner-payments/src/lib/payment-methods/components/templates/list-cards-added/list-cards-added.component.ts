import { Component, EventEmitter, Input, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { TypeCard, cardsColor, cardsTransparent, cardsWhite } from '@aplazo/web-ui';
import { PaymentMethodsEnum } from '../../../enums/payment-methods.enum';

@Component({
  selector: 'aplazo-payments-list-cards-added',
  templateUrl: './list-cards-added.component.html',
  styleUrls: ['./list-cards-added.component.scss'],
})
export class ListCardsAddedComponent {
  public cardsColor: typeof cardsColor = cardsColor;
  public cardsWhite: typeof cardsWhite = cardsWhite;
  public typeCard: typeof TypeCard = TypeCard;
  public cardsTransparent: typeof cardsTransparent = cardsTransparent;

  @Input() formGroup: UntypedFormGroup;

  @Input() txtTemplate: any;

  @Input() card: any = null;

  @Input() btnSubmitDisabled = false;

  @Input() showErrorCardReplaced = false;

  @Input() buttonFixed = false;

  @Input() btnPaymentText: string;

  @Input() showAplazoPoints: boolean = false;

  @Input() availablePoints: number = 0;

  @Input() showCheckAplazoPoints: boolean = false;

  @Input() forceUseAplazoPoints: boolean = false;

  @Input() paymentMethodsEnabled: PaymentMethodsEnum[];

  @Output() useAplazoPointsChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() addCardEvent = new EventEmitter<boolean>();

  @Output() submitForm: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input() showPromoAplazoPoints: boolean = false;
  @Input() promoCashback: string = null;

  readonly genericCard: string = 'https://aplazoassets.s3.us-west-2.amazonaws.com/icons/credit-card.svg';

  public replaceCard(): void {
    this.addCardEvent.emit(true);
  }

  public onSubmit(): void {
    this.formGroup.markAllAsTouched();
    this.submitForm.emit(true);
  }

  public onUseAplazoPoints(): void {
    const value = this.formGroup.get('useAplazoPoints').value;
    this.useAplazoPointsChange.emit(value);
  }
}
