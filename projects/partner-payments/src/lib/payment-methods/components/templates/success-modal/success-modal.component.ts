import { Component, Inject, OnInit } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef } from '@angular/material/legacy-dialog';
import { UiCoreFacade } from '@aplazo/partner-core';
import { Subject, interval, take, takeUntil } from 'rxjs';

@Component({
  selector: 'aplazo-payments-success-modal',
  templateUrl: './success-modal.component.html',
  styleUrls: ['./success-modal.component.scss'],
})
export class SuccessModalComponent implements OnInit {
  progressBarValue = 0;
  interval$ = interval(10);
  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public _dialogRef: MatDialogRef<SuccessModalComponent>,
    private uiCoreFacade: UiCoreFacade
  ) {}

  ngOnInit(): void {
    this.uiCoreFacade.getLoading$.pipe(takeUntil(this._unsubscribe$)).subscribe(loader => {
      if (!loader) {
        this.startTimer(this.data.timeInSeconds ?? 3);
      }
    });
  }
  startTimer(totalTime) {
    const totalTicks = Math.floor(totalTime * 100);
    const increment = 100 / totalTicks;
    this.interval$.pipe(take(totalTicks)).subscribe({
      next: tick => {
        this.progressBarValue = (tick + 1) * increment;
      },
      complete: () => {
        this._dialogRef.close();
      },
    });
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
