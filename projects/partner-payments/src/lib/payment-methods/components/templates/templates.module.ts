import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListCardsAddedComponent } from './list-cards-added/list-cards-added.component';
import { AddCardTemplateComponent } from './add-card-template/add-card-template.component';
import { LogoImageModule, WebUiModule } from '@aplazo/web-ui';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { SuccessModalComponent } from './success-modal/success-modal.component';
import { MatLegacyProgressBarModule as MatProgressBarModule } from '@angular/material/legacy-progress-bar';

@NgModule({
  declarations: [ListCardsAddedComponent, AddCardTemplateComponent, SuccessModalComponent],
  exports: [ListCardsAddedComponent, AddCardTemplateComponent, SuccessModalComponent],
  imports: [CommonModule, WebUiModule, LogoImageModule, MatProgressBarModule],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'es-MX' },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: { dateInput: 'DD/MM/YYYY' },
        display: {
          dateInput: 'DD/MM/YYYY',
          monthYearLabel: 'MMM YYYY',
          dateA11yLabel: 'LL',
          monthYearA11yLabel: 'MMMM-YYYY',
        },
      },
    },
  ],
})
export class TemplatesModule {}
