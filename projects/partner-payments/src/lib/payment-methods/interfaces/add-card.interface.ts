import { ICardToken } from './payment-providers.interface';

export interface IAddCardComponent {
  showCloseButton?: boolean;
  cardName?: string;
  hasCards?: boolean;
  isCustomer?: boolean;
}

export interface ICardIdentifier {
  type?: string;
  scheme?: string;
  country?: {
    numeric: string;
    alpha2?: string;
    currency: string;
  };
  isValid?: string | boolean;
}

export interface IBinException {
  message: string;
}

export interface ICardAddPayload {
  card_brand: string;
  card_type: string;
  platform: string;
  name: string;
  lastName: string;
  cards: ICardToken[];
}

export interface IAddCard {
  cardName: string;
  cardLastName: string;
  cardSecondLastName: string;
  cardNumber: string;
  expiration: string;
  cvc: string;
}

export interface ICardValid {
  isValid: boolean;
  cardType: string;
  brand?: string;
}
