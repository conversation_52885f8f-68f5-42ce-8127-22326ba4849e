import { HttpErrorResponse } from '@angular/common/http';
import { ICard, PluginIntegrations } from '@aplazo/partner-core';
import { PaymentLinksMethods } from '../enums/payment-links-methods';
import { PaymentMethodsTypes, PaymentTypes } from '../enums/payment-types.enum';
export interface IPayment {
  paymentType: PaymentTypes;
  loanId: number;
  transactionId?: number;
  schemeId?: number;
  response: IPaymentResponse;
}

export interface IPaymentResponse {
  paymentSuccess?: boolean;
  response?: IPaymentResponsePayload;
  error?: HttpErrorResponse;
}

export interface IPaymentResponsePayload {
  confirmUrl?: string;
  cartId?: string;
  merchantId?: number;
  type?: PluginIntegrations;
  paymentLink?: string;
  creditAvailable?: number;
  folioCodi?: string;
  confirmSentCodi?: boolean;
  points?: number;
}

export interface IPaymentMethod {
  type: PaymentMethodsTypes;
  method: ICard | IPaymentLink;
  cardNeedsToBeReplace?: boolean;
  requiredCVV?: boolean;
  challengeToken?: string;
  challengeTokenRequired?: boolean;
}

export interface IPaymentLink {
  type: PaymentLinksMethods;
}
