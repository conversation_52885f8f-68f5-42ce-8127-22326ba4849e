export interface IRiskValidationResult {
  success: boolean;
  error?: any;
}
export interface IRiskEvaluator {
  challenges: IChallenges;
}

export interface IChallenges {
  DOB?: boolean;
  KYC?: boolean;
  EMAIL_OTP?: boolean;
  BIOMETRIC?: boolean;
}

export interface RiskArguments {
  moduleArguments: ModuleArguments;
}

export interface ModuleArguments {
  DOB: Date | string;
  KYC: number | string; // TBD
  EMAIL_OTP: string | any; // TBD
  BIOMETRIC: any; // TBD
}
