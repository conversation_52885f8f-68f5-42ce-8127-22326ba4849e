export const VGSErrorCodes = [1001, 1010, 1015, 1017];

type VGSErrorCode = (typeof VGSErrorCodes)[number];

export interface IVgsErrorCodes {
  [key: VGSErrorCode]: string;
}

export enum VGSFormFields {
  CardholderName = 'cardholder-name',
  CardNumber = 'card-number',
  CardExpirationDate = 'card-expiration-date',
  CardSecurityCode = 'card-security-code',
}

export interface IVGSFormValue {
  errors: Array<VGSErrorCode>;
  isValid: boolean;
  isTouched?: boolean;
}

export interface VGSConfigValues {
  vaultId: string;
  environment: string;
  version: string;
}

export type IErrorsInterface = Record<VGSFormFields, IVGSFormValue>;

export interface VGSDataBody {
  card_expiration_date: string;
  card_expiration_month: string;
  card_expiration_year: string;
  card_number: string;
  card_security_code: string;
  cardholder_name: string;
  device_session?: string;
  card_type?: string;
  bin: string;
  platform?: string;
}

export interface VGSResponse {
  code: string;
}
