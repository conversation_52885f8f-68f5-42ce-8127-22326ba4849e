import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaymentMethodsComponent } from './payment-methods/payment-methods.component';
import { StoreModule } from '@ngrx/store';
import { PaymentMethodsFeatureName } from './store/state/payment-methods.state';
import { EffectsModule } from '@ngrx/effects';
import { PaymentEffects } from './store/effects/payment.effects';
import { WebUiModule } from '@aplazo/web-ui';
import { paymentMethodsReducer } from './store/reducers/payment-methods.reducer';
import { AddCardComponent } from './components/add-card/add-card.component';
import { ComponentsModule } from './components/components.module';
import { IncodeLoginModule } from '@aplazo/client-verification';

@NgModule({
  declarations: [PaymentMethodsComponent, AddCardComponent],
  imports: [
    CommonModule,
    StoreModule.forFeature(PaymentMethodsFeatureName, paymentMethodsReducer),
    EffectsModule.forFeature([PaymentEffects]),
    WebUiModule,
    ComponentsModule,
    IncodeLoginModule,
  ],
  exports: [PaymentMethodsComponent, AddCardComponent],
})
export class PaymentMethodsModule {}
