<aplazo-payments-list-cards-added
  *ngIf="checkoutInfo"
  [card]="cardAdded"
  [formGroup]="formGroup"
  [txtTemplate]="txtTemplate?.PAYMENT_METHODS"
  [btnSubmitDisabled]="buttonSubmitDisabled"
  [showErrorCardReplaced]="cardNeedsToBeReplaced"
  [buttonFixed]="paymentButtonFixed"
  [btnPaymentText]="paymentButtonText"
  [availablePoints]="schemeSelected?.aplazo_points_amounts?.aplazo_points_used"
  [showCheckAplazoPoints]="showCheckAplazoPoints"
  [forceUseAplazoPoints]="forceUseAplazoPoints"
  [paymentMethodsEnabled]="paymentMethodsEnabled"
  (addCardEvent)="openAddCard()"
  (submitForm)="submitForm()"
  (useAplazoPointsChange)="onUseAplazoPointsChange($event)"
  [showPromoAplazoPoints]="showPromoAplazoPoints"
  [promoCashback]="promoCashback"
></aplazo-payments-list-cards-added>
