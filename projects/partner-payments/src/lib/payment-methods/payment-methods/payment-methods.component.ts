import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Subject, take, takeUntil } from 'rxjs';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { UiCoreFacade } from '@aplazo/partner-core';
import { PaymentFacade } from '../store/facades/payment.facade';
import { AddCardComponent } from '../components/add-card/add-card.component';
import { IPayment } from '../interfaces/payment.interface';
import { TranslocoService } from '@ngneat/transloco';
import { ICheckoutInfo, IScheme } from '../../checkout/interfaces/checkout-info.interface';
import { ModalCvvComponent } from '../../checkout/components/molecules/modal-cvv/modal-cvv.component';
import { PaymentMethodsEnum } from '../enums/payment-methods.enum';
import { ModalAccountValidationComponent } from '../../checkout/components/molecules/modal-account-validation/modal-account-validation.component';
import { PlanConfirmationFacade } from '../../checkout/store/facades/plan-confirmation.facade';
import { RiskValidationService } from '../services/risk-validation/risk-validation.service';
import { IRiskEvaluator } from '../interfaces/risk-validation.interface';

@Component({
  selector: 'aplazo-payments-payment-methods',
  templateUrl: './payment-methods.component.html',
  styleUrls: ['./payment-methods.component.scss'],
})
export class PaymentMethodsComponent implements OnInit, OnChanges, OnDestroy {
  @Input() paymentData: IPayment;
  @Input() btnSubmitDisabled: boolean;
  @Input() btnPaymentText: string;
  @Input() paymentButtonFixed: boolean;
  @Input() openAddCardModal: boolean;
  @Input() showCheckAplazoPoints: boolean = false;
  @Input() useAplazoPoints: boolean = false;
  @Input() forceUseAplazoPoints: boolean = false;
  @Input() customerType: 'BNPL' | 'PAY_NOW';
  @Output() cardAddedEvent: EventEmitter<boolean>;
  @Output() useAplazoPointsChange: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() checkoutInfo: ICheckoutInfo = null;
  @Input() schemeSelected: IScheme = null;
  @Input() paymentMethodsEnabled: PaymentMethodsEnum[];
  @Input() showPromoAplazoPoints: boolean = false;
  @Input() promoCashback: string = null;

  // private cardHasBeenUpdated = new BehaviorSubject<boolean>(false); // wait until this feature be in apps

  public formGroup: UntypedFormGroup;

  public cardAdded = null;

  public txtTemplate: any;

  private _unsubscribe$: Subject<boolean> = new Subject<boolean>();
  private _paymentMethodSelected: string;
  private _cardNeedsToBeReplaced: boolean;

  constructor(
    private _fb: UntypedFormBuilder,
    private _dialog: MatDialog,
    private _paymentFacade: PaymentFacade,
    private _translocoService: TranslocoService,
    private _uiFacade: UiCoreFacade,
    private _planConfirmationFacade: PlanConfirmationFacade,
    private _riskValidationService: RiskValidationService
  ) {
    this.cardAddedEvent = new EventEmitter<boolean>();
  }

  public ngOnInit(): void {
    this.formGroup = this._fb.group({
      paymentMethodSelected: [null, Validators.required],
      useAplazoPoints: [{ value: this.useAplazoPoints, disabled: this.forceUseAplazoPoints }],
    });
    this._subscribePaymentMethodSelectedValueChange();
    this._subscribeTranslate();
    this._subscribeCardNeedsToBeReplaced();
  }

  public ngOnChanges(): void {
    if (!this.formGroup) {
      return;
    }

    if (this.checkoutInfo.card && this.paymentMethodsEnabled) {
      this._paymentFacade.setRequiredCVV(this.checkoutInfo.card.cvv_update);
      this._setCardData();
    }

    if (this.formGroup?.get('paymentMethodSelected').value) {
      this._paymentMethodSelected = this.formGroup?.get('paymentMethodSelected').value;
    } else {
      if (this.customerType === 'BNPL') {
        if (this.checkoutInfo.card && this.paymentMethodsEnabled.includes('card')) {
          this._paymentMethodSelected = 'card';
        } else if (this.paymentMethodsEnabled.includes('codi')) {
          this._paymentMethodSelected = 'codi';
        } else {
          this._paymentMethodSelected = null;
        }
      } else if (this.customerType === 'PAY_NOW') {
        this._paymentMethodSelected = 'codi';
      }
    }
    this.formGroup.controls.paymentMethodSelected.setValue(this._paymentMethodSelected);
    this._paymentFacade.setPayment(this.paymentData);
  }

  private _subscribeTranslate(): void {
    this._translocoService
      .selectTranslateObject('PAYMENT_METHODS_MODULE')
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe(txtTemplate => {
        this.txtTemplate = txtTemplate;
      });
  }

  private _subscribePaymentMethodSelectedValueChange(): void {
    this.formGroup
      .get('paymentMethodSelected')
      .valueChanges.pipe(takeUntil(this._unsubscribe$))
      .subscribe((paymentMethod: string) => {
        this._paymentMethodSelected = paymentMethod;
      });
  }

  private _subscribeCardNeedsToBeReplaced(): void {
    this._paymentFacade.cardNeedsToBeReplaced$.pipe(takeUntil(this._unsubscribe$)).subscribe(cardNeedsToBeReplaced => {
      this._cardNeedsToBeReplaced = cardNeedsToBeReplaced;
    });
  }

  private _setCardData(): void {
    this.cardAdded = {
      id: 'card',
      paymentMethod: 'card',
      paymentMethodText: this.checkoutInfo.card?.card_number,
      brand: this.checkoutInfo.card?.brand ?? null,
    };
  }

  public get cardNeedsToBeReplaced(): boolean {
    return this._cardNeedsToBeReplaced;
  }

  public openAddCard(): void {
    this._dialog
      .open(AddCardComponent, {
        disableClose: true,
        maxWidth: '400px',
        height: '1050px',
        maxHeight: '100vh',
        width: '100%',
        data: {
          cardName: '',
          showCloseButton: true,
          hasCards: this.checkoutInfo.card,
        },
      })
      .afterClosed()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((cardAdded: boolean) => {
        this.cardAddedEvent.emit(cardAdded);
        if (cardAdded) {
          this._paymentFacade.setRequiredCVV(true);
          this._paymentFacade.setCardNeedsToBeReplaced(false);
          // this.cardHasBeenUpdated.next(true); // wait until this feature be in apps
        }
      });
  }

  public onUseAplazoPointsChange(value) {
    this.useAplazoPointsChange.emit(value);
  }

  public submitForm(): void {
    this.getRiskChallengeEvaluation();
  }

  private getRiskChallengeEvaluation() {
    const applyChallenge = this.checkoutInfo.customer_type === 'BNPL';
    this._paymentFacade.setChallengeTokenRequired(applyChallenge);

    if (this.checkoutInfo.customer_type === 'BNPL') {
      this._uiFacade.setLoading(true);
      this._paymentFacade.challengeToken$.pipe(take(1)).subscribe(challengeToken => {
        if (challengeToken) {
          this.continuePayment(challengeToken);
        } else {
          this._riskValidationService.getChallengeEvaluation(this.checkoutInfo.id.toString()).subscribe({
            next: (res: IRiskEvaluator) => {
              this._uiFacade.setLoading(false);
              if (res?.challenges?.DOB) {
                this.openAccountValidationModal();
              } else {
                this.continuePayment();
              }
            },
            error: err => {
              console.error(err);
              this._uiFacade.setLoading(false);
              const needsDOBvalidation = err.error.data?.active_challenges?.some(challenge => challenge === 'DOB');
              if (needsDOBvalidation) {
                this.openAccountValidationModal();
              } else {
                this.continuePayment();
              }
            },
          });
        }
      });
    } else {
      this.continuePayment();
    }
  }

  private openAccountValidationModal(): void {
    this._dialog
      .open(ModalAccountValidationComponent, {
        disableClose: false,
        maxWidth: '400px',
        width: '100%',
        data: { ...this.txtTemplate.MODAL_ATO, loanId: this.checkoutInfo.id },
      })
      .afterClosed()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe(data => {
        if (data?.continue) {
          this._paymentFacade.setChallengeToken(data?.challengeToken);
          this.continuePayment(data?.challengeToken);
        }
      });
  }

  private continuePayment(challengeToken: string = null): void {
    if (String(this._paymentMethodSelected).toLowerCase().includes('codi')) {
      this._planConfirmationFacade.setUsePaymentCodi(true);
      this.initiatePayment();
    } else {
      this._planConfirmationFacade.setUsePaymentCodi(false);
      if (this.requiresCvvUpdate()) {
        this._uiFacade.setLoading(false);
        this.openCvvUpdateModal(challengeToken);
      } else {
        this.initiatePayment(challengeToken);
      }
    }
  }

  private initiatePayment(challengeToken: string = null): void {
    this._uiFacade.setLoading(true);
    this._planConfirmationFacade.usePaymentCodi$.pipe(take(1)).subscribe(useCodi => {
      this._paymentFacade.sendPayment(
        null,
        this.checkoutInfo.id,
        this.schemeSelected.id,
        this.useAplazoPoints,
        useCodi,
        this.checkoutInfo.customer_type === 'PAY_NOW',
        challengeToken
      );
    });
  }

  private calculateTotalAmount(): number {
    return this.useAplazoPoints ? this.schemeSelected.aplazo_points_amounts.total : this.schemeSelected.total;
  }

  private requiresCvvUpdate(): boolean {
    return this.checkoutInfo.card.cvv_update && this.calculateTotalAmount() > 0;
  }

  private openCvvUpdateModal(challengeToken: string = null): void {
    const data = {
      title: this.txtTemplate?.PAYMENT_METHODS?.MODAL_CVV?.title,
      card: this.checkoutInfo.card,
      cvv: {
        hint: this.txtTemplate?.PAYMENT_METHODS?.MODAL_CVV?.cvv?.hint,
        placeholder: this.txtTemplate?.PAYMENT_METHODS?.MODAL_CVV?.cvv?.placeholder,
      },
      leyend: this.txtTemplate?.PAYMENT_METHODS?.MODAL_CVV?.leyend,
      buttonText: this.txtTemplate?.PAYMENT_METHODS?.MODAL_CVV?.buttonConfirmPayment,
      loanId: this.checkoutInfo.id,
      schemeId: this.schemeSelected.id,
      useAplazoPoints: this.useAplazoPoints,
      showLeyend: this.checkoutInfo.card.bbva && this.checkoutInfo.card.cvv_update,
      isPayNow: this.checkoutInfo.customer_type === 'PAY_NOW',
      challengeToken: challengeToken,
    };
    this._dialog.open(ModalCvvComponent, {
      disableClose: false,
      maxWidth: '400px',
      width: '100%',
      data: data,
    });
  }

  public get paymentButtonText(): string {
    return this.btnPaymentText;
  }

  // Validation to enable or disable the submit button
  public get buttonSubmitDisabled(): boolean {
    return (
      this.formGroup.invalid ||
      this.btnSubmitDisabled ||
      (this.cardNeedsToBeReplaced && this._paymentMethodSelected === 'card') ||
      (this.cardAdded === null && this.customerType === 'BNPL')
    );
  }

  public ngOnDestroy(): void {
    this._unsubscribe$.next(true);
    this._unsubscribe$.complete();
  }
}
