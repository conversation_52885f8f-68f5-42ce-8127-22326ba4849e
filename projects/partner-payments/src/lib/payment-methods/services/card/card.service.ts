import { HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { PartnerCoreEnvironment, PARTNER_CORE_ENVIRONMENT } from '@aplazo/partner-core';
import { catchError, map, Observable, of, switchMap } from 'rxjs';
import { ICardIdentifier, ICardValid } from '../../interfaces/add-card.interface';

@Injectable({
  providedIn: 'root',
})
export class CardService {
  constructor(
    private _http: HttpClient,
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
  ) {}
  public updateCvv(cvv: string): Observable<boolean> {
    return this._http.put(`${this._environment.api.customCard}customer/card/cvv`, { cvv }).pipe(
      map(() => true),
      catchError(() => of(false))
    );
  }

  public _isCardValid$(bin: string): Observable<ICardValid> {
    // Avoid the lambdas because QA needs to add this card to tests some cases
    if (!this._environment.IS_PRODUCTION && bin === '55555555') {
      return of({
        isValid: true,
        cardType: 'debit',
        brand: 'MASTERCARD',
      });
    } else {
      return this._cardIdentifier$(bin).pipe(
        switchMap(({ isValid, cardType, brand }) => {
          if (isValid) {
            return of({
              isValid: isValid,
              cardType: cardType,
              brand: brand,
            });
          }
          return this._exceptionBins$(isValid, bin, cardType, brand);
        })
      );
    }
  }

  private _cardIdentifier$(bin: string): Observable<ICardValid> {
    const headers = new HttpHeaders().set('x-api-key', this._environment.AWS.HTTP_HEADER.VALUE);
    return this._http.post(`${this._environment.api.customCard}bin/identify`, { card_number: bin }, { headers }).pipe(
      map((card: ICardIdentifier) => {
        return {
          isValid: card?.country?.alpha2 === 'MX' && card?.isValid === 'true',
          cardType: card?.type ?? 'credit',
          brand: (card?.scheme?.includes('American') ? 'AMEX' : card?.scheme)?.toUpperCase() ?? '',
        };
      }),
      catchError(({ error }) => {
        return of({
          isValid: (error?.isValid ?? '') === 'true',
          cardType: error?.type ?? 'credit',
          brand: (error?.scheme.includes('American') ? 'AMEX' : error?.scheme)?.toUpperCase() ?? '',
        });
      })
    );
  }

  private _exceptionBins$(isValid: boolean, bin: string, cardType: string, brand?: string): Observable<ICardValid> {
    return this._http.get(`${this._environment.api.customCard}bin/blacklist/${bin}`).pipe(
      map(() => {
        return { isValid: true, cardType, brand };
      }),
      catchError((error: HttpResponse<any>) => {
        console.error(error);
        return of({
          isValid: isValid,
          cardType,
          brand,
        });
      })
    );
  }
}
