import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { PartnerCoreEnvironment, PARTNER_CORE_ENVIRONMENT } from '@aplazo/partner-core';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CodiService {
  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,

    private _http: HttpClient
  ) {}

  private codiError$ = new Subject<HttpErrorResponse>();

  public setCodiError(err: HttpErrorResponse) {
    this.codiError$.next(err);
  }

  public getCodiError$(): Observable<HttpErrorResponse> {
    return this.codiError$.asObservable();
  }

  public codiStatus(folioCodi: string): Observable<any> {
    return this._http.post(`${this._environment.api.statusCodi}api/v1/codi/status/info/${folioCodi}`, {});
  }
}
