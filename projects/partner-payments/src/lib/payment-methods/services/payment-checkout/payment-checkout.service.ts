import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { KountService } from '@aplazo/partner-analytics';
import { PartnerCoreEnvironment, PARTNER_CORE_ENVIRONMENT } from '@aplazo/partner-core';
import { Observable, switchMap } from 'rxjs';
import { CardService } from '../card/card.service';
import { is } from 'date-fns/locale';

@Injectable({
  providedIn: 'root',
})
export class PaymentCheckoutService {
  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _kountService: KountService,
    private _cardService: CardService,
    private _http: HttpClient
  ) {
    this._kountService.initKount();
  }

  public payLoan$(
    cvv: string = null,
    loanId: number,
    schemeId: number,
    useAplazoPoints: boolean,
    isPaymentCodi: boolean = false,
    isPayNow: boolean,
    challengeToken: string
  ): Observable<any> {
    if (cvv) {
      return this._cardService
        .updateCvv(cvv)
        .pipe(
          switchMap(() =>
            this.confirmPayment(loanId, schemeId, useAplazoPoints, isPaymentCodi, isPayNow, challengeToken)
          )
        );
    }
    return this.confirmPayment(loanId, schemeId, useAplazoPoints, isPaymentCodi, isPayNow, challengeToken);
  }

  public confirmPayment(
    loanId: number,
    schemeId,
    useAplazoPoints: boolean,
    isPaymentCodi: boolean,
    isPayNow: boolean,
    challengeToken: string
  ): Observable<any> {
    let headers = new HttpHeaders().set('platform', 'web');
    if (challengeToken) {
      headers = headers.set('X-Risk-Token', challengeToken);
    }
    return this._http.post(
      `${this._environment.api.msPayments}api/v1/payment/first-installment?loan-id=${loanId}`,
      { schemeId, sessionId: this._kountService.sessionId, useAplazoPoints, isPaymentCodi, isPayNow },
      {
        headers,
      }
    );
  }
}
