import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { PartnerCoreEnvironment, PARTNER_CORE_ENVIRONMENT } from '@aplazo/partner-core';

declare const OpenPay: {
  setId: (merchantId: string) => void;
  setApiKey: (key: string) => void;
  setSandboxMode: (isProd: boolean) => void;
  deviceData: {
    setup: () => string;
  };
  token: {
    create: (payload, success, error) => void;
  };
  card: {
    validateExpiry: (month: string, year: string) => boolean;
  };
};

@Injectable({
  providedIn: 'root',
})
export class OpenpayService {
  private _deviceSession: string = null;
  private _renderer: Renderer2;

  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _rendererFactory: RendererFactory2
  ) {}

  public setOpenPay(): void {
    OpenPay.setId(this._environment.OPENPAY.MERCHANT_ID);
    OpenPay.setApiKey(this._environment.OPENPAY.KEY);
    OpenPay.setSandboxMode(!this._environment.IS_PRODUCTION);
    this._deviceSession = OpenPay.deviceData.setup();
  }

  public getDeviceSession(): string {
    return this._deviceSession ?? null;
  }

  public _addOpenpayScripts(): void {
    if (this._deviceSession !== null) {
      return;
    }
    this._renderer = this._rendererFactory.createRenderer(null, null);
    if (this._environment.CHROME_EXTENSION?.isChromeExtension) {
      this._addOpenpayScriptsAntifraud();
    } else {
      const script = this._renderer.createElement('script');
      this._renderer.setProperty(script, 'src', this._environment.OPENPAY.SDK);
      this._renderer.appendChild(document.body, script);
      script.onload = () => {
        this._addOpenpayScriptsAntifraud();
      };
    }
  }

  private _addOpenpayScriptsAntifraud(): void {
    if (this._environment.CHROME_EXTENSION?.isChromeExtension) {
      this.setOpenPay();
    } else {
      const script = this._renderer.createElement('script');
      this._renderer.setProperty(script, 'src', this._environment.OPENPAY.SDK_DATA);
      this._renderer.appendChild(document.body, script);
      script.onload = () => {
        this.setOpenPay();
      };
    }
  }
}
