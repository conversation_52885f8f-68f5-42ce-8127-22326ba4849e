import { Inject, Injectable } from '@angular/core';
import { PARTNER_CORE_ENVIRONMENT, PartnerCoreEnvironment, UiCoreFacade } from '@aplazo/partner-core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class RiskValidationService {
  constructor(
    @Inject(PARTNER_CORE_ENVIRONMENT)
    private _environment: PartnerCoreEnvironment,
    private _http: HttpClient
  ) {}

  public getChallengeEvaluation(loanId: string): Observable<any> {
    return this._http.get(`${this._environment.api.riskEngine}api/v1/checkout/risks?loanId=${loanId}`);
  }

  public validateChallenge(loanId: number, dob: string): Observable<any> {
    const moduleArguments = {
      moduleArguments: {
        DOB: dob,
      },
    };

    return this._http.post(
      `${this._environment.api.riskEngine}api/v1/loans/${loanId}/challenges/-/validate`,
      moduleArguments
    );
  }
}
