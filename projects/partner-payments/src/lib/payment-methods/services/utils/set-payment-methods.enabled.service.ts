import { Injectable } from '@angular/core';
import { PaymentEnabled, PaymentMethodsEnum } from '../../enums/payment-methods.enum';

@Injectable({
  providedIn: 'root',
})
export class PaymentMethodsEnabledService {

  public getPaymentMethodsEnabled(paymentEnabled: PaymentEnabled): PaymentMethodsEnum[] {
    return Object.entries(paymentEnabled)
      .filter(([_, value]) => value)
      .map(([key]) => key as PaymentMethodsEnum);
  }
}
