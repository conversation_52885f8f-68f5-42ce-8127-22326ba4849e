import { IRiskValidation } from '@aplazo/partner-core';
import { createAction, props } from '@ngrx/store';
import { PaymentTypes } from '../../enums/payment-types.enum';
import { IPaymentResponse } from '../../interfaces/payment.interface';

export const reset = createAction('[Partner Payments Module] Reset Payment');

export const setPayment = createAction(
  '[Partner Payments Module] Set Payment Type',
  props<{
    paymentType: PaymentTypes;
    loanId: number;
    transactionId?: number;
    schemeId?: number;
  }>()
);

export const setRequiredCVV = createAction(
  '[Partner Payments Module] Card - change required cvv input',
  props<{ requiredCVV: boolean }>()
);

export const setCardNeedsToBeReplace = createAction(
  '[Partner Payments Module] Card - Set Card needs to be replace',
  props<{ cardNeedsToBeReplace: boolean }>()
);

export const payLoan = createAction(
  '[Partner Payments Module] Pay loan',
  props<{
    cvv?: string;
    loanId?: number;
    schemeId?: number;
    useAplazoPoints?: boolean;
    isPaymentCodi?: boolean;
    isPayNow: boolean;
    challengeToken: string;
  }>()
);

export const payLoanSuccess = createAction(
  '[Partner Payments Module] Pay Loan Success',
  props<{
    response: IPaymentResponse;
  }>()
);

export const confirmFirstInstallmentCoDiSuccess = createAction(
  '[Partner Payments Module] Pay Loan Success',
  props<{
    response: IPaymentResponse;
  }>()
);

export const setChallengeToken = createAction(
  '[Partner Payments Module] Challenge Token - Set challenge Token ',
  props<{ challengeToken: string }>()
);
export const setChallengeTokenRequired = createAction(
  '[Partner Payments Module] Challenge Token - Set challenge Token required ',
  props<{ challengeTokenRequired: boolean }>()
);
