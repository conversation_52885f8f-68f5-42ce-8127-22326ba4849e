import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, switchMap } from 'rxjs/operators';
import { IPaymentResponse, IPaymentResponsePayload } from '../../interfaces/payment.interface';
import * as paymentActions from '../actions/payment-methods.actions';
import { PaymentCheckoutService } from '../../services/payment-checkout/payment-checkout.service';
import { CodiService } from '../../services/codi/codi.service';
import { PlanConfirmationFacade } from '../../../checkout/store/facades/plan-confirmation.facade';

@Injectable()
export class PaymentEffects {
  constructor(
    private _actions$: Actions,
    private _paymentCheckoutService: PaymentCheckoutService,
    private _codiSvc: CodiService,
    private _planConfirmationFacade: PlanConfirmationFacade
  ) {}

  payLoan$ = createEffect(() =>
    this._actions$.pipe(
      ofType(paymentActions.payLoan),
      switchMap(({ cvv, loanId, schemeId, useAplazoPoints, isPaymentCodi, isPayNow, challengeToken }) => {
        return this._paymentCheckoutService
          .payLoan$(cvv, loanId, schemeId, useAplazoPoints, isPaymentCodi, isPayNow, challengeToken)
          .pipe(
            map((paymentResponse: IPaymentResponsePayload) => {
              if (paymentResponse.folioCodi) {
                this._planConfirmationFacade.setFolioCodi(paymentResponse.folioCodi);
              }
              const response: IPaymentResponse = { paymentSuccess: true, response: paymentResponse };
              if (isPaymentCodi && response.response.confirmSentCodi) {
                return paymentActions.confirmFirstInstallmentCoDiSuccess({ response });
              } else {
                return paymentActions.payLoanSuccess({ response });
              }
            }),
            catchError((error: HttpErrorResponse) => {
              const response = { paymentSuccess: false, error };
              if (isPaymentCodi && error.error.code !== '4600') {
                this._codiSvc.setCodiError(error);
                return [paymentActions.confirmFirstInstallmentCoDiSuccess({ response })];
              } else {
                return [paymentActions.payLoanSuccess({ response })];
              }
            })
          );
      })
    )
  );
}
