import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import * as paymentActions from '../actions/payment.actions';
import { PaymentMethodsState } from '../state/payment-methods.state';
import * as paymentSelectors from '../selectors/payment.selectors';
import { filter } from 'rxjs';
import { IPayment } from '../../interfaces/payment.interface';

@Injectable({ providedIn: 'root' })
export class PaymentFacade {
  constructor(private store: Store<PaymentMethodsState>) {}

  public payLoan$ = this.store.select(paymentSelectors.paymentResponseSelector).pipe(filter(payLoan => !!payLoan));

  public paymentType$ = this.store
    .select(paymentSelectors.paymentTypeSelector)
    .pipe(filter(paymentType => !!paymentType));

  public paymentLoanId$ = this.store
    .select(paymentSelectors.paymentLoanIdSelector)
    .pipe(filter(paymentLoanId => !!paymentLoanId));

  public paymentTransactionId$ = this.store
    .select(paymentSelectors.paymentLoanIdSelector)
    .pipe(filter(paymentTransactionId => !!paymentTransactionId));

  public paymentSchemeId$ = this.store
    .select(paymentSelectors.paymentShemeIdSelector)
    .pipe(filter(paymentSchemeId => !!paymentSchemeId));

  public paymentMethodSelected$ = this.store.select(paymentSelectors.paymentMethodSelectedSelector);

  public requiredCVV$ = this.store.select(paymentSelectors.requiredCVVSelector);

  public cardNeedsToBeReplaced$ = this.store.select(paymentSelectors.cardNeedsToBeReplacedSelector);

  public challengeToken$ = this.store.select(paymentSelectors.challengeToken);

  public challengeTokenRequired$ = this.store.select(paymentSelectors.challengeTokenRequired);

  public setPayment(payment: IPayment): void {
    this.store.dispatch(paymentActions.payment.setPayment({ ...payment }));
  }

  public sendPayment(
    cvv: string,
    loanId: number,
    schemeId: number,
    useAplazoPoints,
    isPaymentCodi: boolean = false,
    isPayNow: boolean,
    challengeToken: string
  ): void {
    this.store.dispatch(
      paymentActions.payment.payLoan({
        cvv,
        loanId,
        schemeId,
        useAplazoPoints,
        isPaymentCodi,
        isPayNow,
        challengeToken,
      })
    );
  }

  public setCardNeedsToBeReplaced(cardNeedsToBeReplace: boolean): void {
    this.store.dispatch(paymentActions.payment.setCardNeedsToBeReplace({ cardNeedsToBeReplace }));
  }

  public setRequiredCVV(requiredCVV: boolean): void {
    this.store.dispatch(paymentActions.payment.setRequiredCVV({ requiredCVV }));
  }

  public setChallengeToken(challengeToken: string): void {
    this.store.dispatch(paymentActions.payment.setChallengeToken({ challengeToken }));
  }
  public setChallengeTokenRequired(challengeTokenRequired: boolean): void {
    this.store.dispatch(paymentActions.payment.setChallengeTokenRequired({ challengeTokenRequired }));
  }

  public reset(): void {
    this.store.dispatch(paymentActions.payment.reset());
  }
}
