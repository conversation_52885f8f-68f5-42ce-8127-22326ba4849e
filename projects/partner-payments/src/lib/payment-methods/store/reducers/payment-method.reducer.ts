import { createReducer, on, Action } from '@ngrx/store';
import { IPaymentMethod } from '../../interfaces/payment.interface';
import * as paymentActions from '../actions/payment.actions';

export const initialState: IPaymentMethod = {
  type: null,
  method: null,
  cardNeedsToBeReplace: false,
  requiredCVV: false,
  challengeToken: null,
  challengeTokenRequired: false,
};

const _paymentMethodReducer = createReducer(
  initialState,
  on(paymentActions.payment.setRequiredCVV, (state, { requiredCVV }) => ({
    ...state,
    requiredCVV,
  })),
  on(paymentActions.payment.setCardNeedsToBeReplace, (state, { cardNeedsToBeReplace }) => ({
    ...state,
    cardNeedsToBeReplace,
  })),
  on(paymentActions.payment.setChallengeToken, (state, { challengeToken }) => ({
    ...state,
    challengeToken,
  })),
  on(paymentActions.payment.setChallengeTokenRequired, (state, { challengeTokenRequired }) => ({
    ...state,
    challengeTokenRequired,
  }))
);

export function paymentMethodReducer(state: IPaymentMethod, action: Action): IPaymentMethod {
  return _paymentMethodReducer(state, action);
}
