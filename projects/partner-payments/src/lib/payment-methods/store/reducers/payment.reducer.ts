import { createReducer, on, Action } from '@ngrx/store';
import { IPayment } from '../../interfaces/payment.interface';
import * as paymentActions from '../actions/payment.actions';

export const initialState: IPayment = null;

const _paymentReducer = createReducer(
  initialState,
  on(paymentActions.payment.setPayment, (state, { paymentType, loanId, transactionId, schemeId }) => ({
    ...state,
    paymentType,
    loanId,
    transactionId,
    schemeId
  })),
  on(paymentActions.payment.payLoanSuccess, (state, { response }) => {
    return {
      ...state,
      response,
    };
  }),
  on(paymentActions.payment.confirmFirstInstallmentCoDiSuccess, (state, { response }) => {
    return {
      ...state,
      response,
    };
  }),
  on(paymentActions.payment.reset, () => initialState)
);

export function paymentReducer(state: IPayment, action: Action): IPayment {
  return _paymentReducer(state, action);
}
