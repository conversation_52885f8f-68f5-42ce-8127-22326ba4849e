import { createSelector } from '@ngrx/store';
import { paymentMethodsRootSelector } from './index.selectors';

export const paymentResponseSelector = createSelector(paymentMethodsRootSelector, state => state.payment?.response);

export const paymentSelector = createSelector(paymentMethodsRootSelector, state => state.payment);

export const paymentLoanIdSelector = createSelector(paymentMethodsRootSelector, state => state.payment.loanId);

export const paymentTypeSelector = createSelector(paymentMethodsRootSelector, state => state.payment.paymentType);

export const paymentTransactionIdSelector = createSelector(
  paymentMethodsRootSelector,
  state => state.payment?.transactionId
);

export const paymentShemeIdSelector = createSelector(paymentMethodsRootSelector, state => state.payment?.schemeId);

export const paymentMethodSelectedSelector = createSelector(paymentMethodsRootSelector, state => state.paymentMethod);

export const paymentLinkOxxoSpeiSelector = createSelector(
  paymentMethodsRootSelector,
  state => state.payment?.response.response?.paymentLink
);

export const requiredCVVSelector = createSelector(paymentMethodsRootSelector, state => state.paymentMethod.requiredCVV);

export const cardNeedsToBeReplacedSelector = createSelector(
  paymentMethodsRootSelector,
  state => state.paymentMethod.cardNeedsToBeReplace
);

export const challengeToken = createSelector(paymentMethodsRootSelector, state => state.paymentMethod.challengeToken);

export const challengeTokenRequired = createSelector(
  paymentMethodsRootSelector,
  state => state.paymentMethod.challengeTokenRequired
);
