# Test de Implementación - Query Parameters

## Pasos para Probar

### 1. Iniciar la aplicación

```bash
cd /Users/<USER>/Desktop/angular.partner-payments
npm start
```

### 2. URLs de Prueba

#### Con Query Parameters (Cashi Flow):

```
http://localhost:4200/walmart-cashi-payment?offerId=test-123&installments=3
```

#### Sin Query Parameters (Flujo Normal):

```
http://localhost:4200/walmart-cashi-payment
```

### 3. Logs a Verificar en la Consola

#### Con Query Parameters:

```
🔍 PaymentOrchestratorComponent - Current URL: /walmart-cashi-payment?offerId=test-123&installments=3
🔍 Cashi flow detected in PaymentOrchestratorComponent
🔍 Found Cashi parameters in referrer: { installments: "3", offerId: "test-123" }
🔍 Query parameters: { queryInstallments: 3, queryOfferId: "test-123" }
🔍 Available schemes: [array of schemes]
🔍 Looking for scheme with 3 installments
🔍 Available schemes: [{ id: X, installments: 3 }, { id: Y, installments: 5 }]
✅ Found single matching scheme: {scheme object}
✅ Preselection successful, going to plan confirmation
```

#### Sin Query Parameters:

```
🔍 PaymentOrchestratorComponent - Current URL: /walmart-cashi-payment
🔄 Using dynamic installments flow
```

### 4. Comportamiento Esperado

#### Con installments=3:

- ✅ Debería preseleccionar el esquema de 3 cuotas
- ✅ Debería ir directamente a plan confirmation
- ✅ NO debería mostrar la pantalla de selección de cuotas

#### Con installments=5:

- ✅ Debería preseleccionar el esquema de 5 cuotas
- ✅ Debería ir directamente a plan confirmation

#### Sin parámetros:

- ✅ Debería usar el flujo normal (selección de cuotas o esquema único)

### 5. Problemas Potenciales

#### Si el endpoint no devuelve múltiples esquemas:

```
🔍 Available schemes: [{ id: X, installments: 5 }]  // Solo 1 esquema
❌ No scheme found for 3 installments
Available installments: [5]
❌ No matching scheme found, falling back to normal flow
```

#### Si no hay query parameters:

```
🔍 Query parameters: { queryInstallments: null, queryOfferId: null }
🔄 Using dynamic installments flow
```

## Diagnóstico

### Si funciona correctamente:

- Los logs muestran múltiples esquemas disponibles
- La preselección encuentra el esquema correcto
- Va directamente a plan confirmation

### Si no funciona:

- El endpoint solo devuelve 1 esquema (problema del backend)
- Los query parameters no se están capturando correctamente
- Hay un error en la lógica de matching

## Siguiente Paso

Una vez que veas los logs, podremos determinar si:

1. El problema está en el backend (endpoint no devuelve múltiples esquemas)
2. El problema está en la captura de query parameters
3. La implementación funciona correctamente
