<!DOCTYPE html>
<html>
  <head>
    <title>Test Query Parameters</title>
  </head>
  <body>
    <h1>Test Query Parameters</h1>
    <p>Test URLs:</p>
    <ul>
      <li>
        <a href="http://localhost:4200/walmart-cashi-payment?offerId=test-123&installments=3">With 3 installments</a>
      </li>
      <li>
        <a href="http://localhost:4200/walmart-cashi-payment?offerId=test-456&installments=5">With 5 installments</a>
      </li>
      <li><a href="http://localhost:4200/walmart-cashi-payment">Without parameters</a></li>
    </ul>

    <h2>Expected Behavior:</h2>
    <ul>
      <li>
        <strong>With installments=3:</strong> Should preselect 3-installment scheme and go directly to plan confirmation
      </li>
      <li>
        <strong>With installments=5:</strong> Should preselect 5-installment scheme and go directly to plan confirmation
      </li>
      <li><strong>Without parameters:</strong> Should use normal flow (dynamic installments or single scheme)</li>
    </ul>

    <h2>Console Logs to Check:</h2>
    <ul>
      <li>🔍 Query parameters: { queryInstallments: 3, queryOfferId: "test-123" }</li>
      <li>🔍 Available schemes: [array of schemes]</li>
      <li>🔍 Looking for scheme with 3 installments</li>
      <li>✅ Found single matching scheme: {scheme object}</li>
      <li>✅ Preselection successful, going to plan confirmation</li>
    </ul>
  </body>
</html>
