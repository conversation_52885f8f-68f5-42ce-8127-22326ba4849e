{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "preserveSymlinks": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "es2020",
    "lib": [
      "es2022",
      "dom"
    ],
    "paths": {
      "partner-payments": [
        "dist/partner-payments/partner-payments",
        "dist/partner-payments"
      ]
    },
    "useDefineForClassFields": false
  },
  "angularCompilerOptions": {
    "fullTemplateTypeCheck": true,
    "strictInjectionParameters": true,
    "enableIvy": false,
  }
}